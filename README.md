# نظام إدارة المخبز المحاسبي 🥖

نظام محاسبي شامل ومتكامل لإدارة المخابز باللغة العربية مع دعم RTL كامل.

## 🌟 المميزات الرئيسية

### 💼 إدارة شاملة
- **إدارة بيانات المنشأة**: معلومات كاملة عن المخبز والإعدادات
- **نظام المستخدمين**: إدارة متقدمة للمستخدمين والصلاحيات
- **شجرة الحسابات**: نظام محاسبي متكامل وفقاً للمعايير المحاسبية

### 💰 الإدارة المالية
- **الصناديق والبنوك**: إدارة متعددة الصناديق مع ربط بالموظفين
- **سندات القبض والصرف**: نظام سندات ذكي مع قيود تلقائية
- **القيود المحاسبية**: إنشاء تلقائي للقيود مع ضمان التوازن

### 👥 إدارة الموظفين
- **بيانات الموظفين**: سجلات شاملة للموظفين
- **نظام الرواتب**: حساب تلقائي للرواتب والاستحقاقات
- **السلف والخصومات**: إدارة متقدمة للسلف والخصومات

### 📦 إدارة المخزون
- **الأصناف والمنتجات**: إدارة شاملة للخامات والمنتجات
- **وصفات المنتجات**: تحديد مكونات المنتجات وحساب التكلفة
- **حركة المخزون**: تتبع دقيق لجميع حركات المخزون

### 🧾 نظام الفواتير
- **فواتير المبيعات**: نظام متقدم لفواتير المبيعات
- **فواتير المشتريات**: إدارة مشتريات المخبز
- **المرتجعات**: معالجة مرتجعات المبيعات والمشتريات
- **البيع السريع**: واجهة سريعة للمبيعات اليومية

### 🏢 الأصول الثابتة
- **إدارة الأصول**: تسجيل وإدارة الأصول الثابتة
- **الإهلاك التلقائي**: حساب الإهلاك وفقاً لطرق مختلفة

### 📊 التقارير
- **التقارير المالية**: الميزانية وقائمة الدخل وميزان المراجعة
- **تقارير المخزون**: تقارير شاملة عن حالة المخزون
- **تقارير المبيعات**: تحليل مفصل للمبيعات
- **تقارير الموظفين**: تقارير الرواتب والحضور

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework**: Custom MVC Architecture
- **UI**: Bootstrap 5 مع تخصيص للغة العربية
- **Icons**: Font Awesome 6

## 📋 متطلبات النظام

### الحد الأدنى
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- 512MB RAM
- 1GB مساحة تخزين

### الموصى به
- PHP 8.0+
- MySQL 8.0+
- 2GB RAM
- 5GB مساحة تخزين
- SSL Certificate

## 🚀 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/anwarsoft/bakery-system.git
cd bakery-system
```

### 2. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة:
```sql
CREATE DATABASE bakery_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. تشغيل ملفات SQL:
```bash
mysql -u username -p bakery_system < database/bakery_system.sql
mysql -u username -p bakery_system < database/inventory_invoices.sql
mysql -u username -p bakery_system < database/initial_data.sql
```

### 3. تكوين الاتصال
تعديل ملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakery_system');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 644 config/*.php
```

### 5. الوصول للنظام
- الرابط: `http://your-domain.com/anwarsoft`
- اسم المستخدم: `admin`
- كلمة المرور: `password`

## 👤 بيانات الدخول الافتراضية

| الدور | اسم المستخدم | كلمة المرور |
|-------|-------------|------------|
| مدير النظام | admin | password |

⚠️ **هام**: يرجى تغيير كلمة المرور فور تسجيل الدخول الأول.

## 📁 هيكل المشروع

```
anwarsoft/
├── config/                 # ملفات الإعدادات
│   ├── database.php        # إعدادات قاعدة البيانات
│   └── config.php          # الإعدادات العامة
├── database/               # ملفات قاعدة البيانات
│   ├── bakery_system.sql   # الجداول الأساسية
│   ├── inventory_invoices.sql # جداول المخزون والفواتير
│   └── initial_data.sql    # البيانات الأولية
├── includes/               # الملفات المشتركة
│   ├── functions.php       # الدوال المساعدة
│   ├── validation.php      # التحقق من البيانات
│   └── sidebar.php         # الشريط الجانبي
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات التنسيق
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور والأيقونات
├── modules/               # وحدات النظام
│   ├── company/           # بيانات المنشأة
│   ├── accounts/          # شجرة الحسابات
│   ├── users/             # إدارة المستخدمين
│   ├── cash_bank/         # الصناديق والبنوك
│   ├── employees/         # إدارة الموظفين
│   ├── inventory/         # إدارة المخزون
│   ├── invoices/          # الفواتير
│   ├── vouchers/          # السندات
│   ├── assets/            # الأصول الثابتة
│   └── reports/           # التقارير
├── uploads/               # ملفات الرفع
├── index.php              # الصفحة الرئيسية
├── login.php              # صفحة تسجيل الدخول
└── logout.php             # تسجيل الخروج
```

## 🔧 الإعدادات المتقدمة

### إعدادات العملة
النظام مصمم للعمل بالريال اليمني فقط:
- رمز العملة: YER
- اسم العملة: ريال يمني
- رمز العرض: ر.ي

### إعدادات الطباعة
- دعم الطباعة الحرارية والعادية
- تخصيص تنسيق الفواتير
- إمكانية طباعة الشعار

### النسخ الاحتياطي
- نسخ احتياطي تلقائي يومي
- إمكانية النسخ اليدوي
- استعادة البيانات

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من SQL Injection
- نظام صلاحيات متقدم
- تسجيل جميع العمليات
- جلسات آمنة

## 📞 الدعم والمساعدة

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +967-777-123456
- **الموقع**: www.anwarsoft.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📝 سجل التغييرات

راجع ملف [CHANGELOG.md](CHANGELOG.md) لمعرفة آخر التحديثات.

---

© 2024 أنوار سوفت. جميع الحقوق محفوظة.

<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 */

if (!$isLoggedIn) {
    redirectTo('login');
}

// جلب الإحصائيات
$stats = [];
$recentActivities = [];
$alerts = [];

try {
    // إحصائيات المبيعات اليومية
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_invoices,
            COALESCE(SUM(total_amount), 0) as total_sales,
            COALESCE(SUM(paid_amount), 0) as total_paid
        FROM invoices 
        WHERE invoice_type = 'sale' 
        AND DATE(created_at) = CURDATE()
    ");
    $dailySales = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات المخزون
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_products,
            COUNT(CASE WHEN stock_quantity <= min_stock_level THEN 1 END) as low_stock_products
        FROM products 
        WHERE is_active = 1
    ");
    $inventory = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات العملاء
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_customers,
            COALESCE(SUM(current_balance), 0) as total_receivables
        FROM customers 
        WHERE is_active = 1
    ");
    $customers = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات الصندوق
    $stmt = $pdo->query("
        SELECT 
            COALESCE(SUM(current_balance), 0) as total_cash
        FROM cash_boxes 
        WHERE is_active = 1
    ");
    $cash = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // النشاطات الأخيرة
    $stmt = $pdo->query("
        SELECT 
            al.action,
            al.created_at,
            u.full_name
        FROM activity_log al
        LEFT JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT 10
    ");
    $recentActivities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنبيهات المخزون المنخفض
    $stmt = $pdo->query("
        SELECT name, stock_quantity, min_stock_level
        FROM products 
        WHERE stock_quantity <= min_stock_level 
        AND is_active = 1
        LIMIT 5
    ");
    $lowStockProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stats = [
        'daily_sales' => $dailySales,
        'inventory' => $inventory,
        'customers' => $customers,
        'cash' => $cash,
        'low_stock_products' => $lowStockProducts
    ];
    
} catch (PDOException $e) {
    $errors[] = 'خطأ في جلب الإحصائيات: ' . $e->getMessage();
}
?>

<div class="dashboard-container">
    <!-- ترحيب -->
    <div class="welcome-section mb-4">
        <div class="card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 style="color: #333; margin-bottom: 0.5rem;">
                        مرحباً، <?php echo htmlspecialchars($currentUser['full_name']); ?>
                    </h2>
                    <p style="color: #666; margin: 0;">
                        <i class="fas fa-calendar"></i>
                        <?php echo date('l, d F Y'); ?>
                        |
                        <i class="fas fa-clock"></i>
                        <?php echo date('H:i'); ?>
                    </p>
                </div>
                <div>
                    <span class="badge badge-<?php echo $currentUser['role'] === 'admin' ? 'primary' : 'secondary'; ?>">
                        <?php 
                        $roles = [
                            'admin' => 'مدير النظام',
                            'accountant' => 'محاسب',
                            'cashier' => 'كاشير',
                            'manager' => 'مدير'
                        ];
                        echo $roles[$currentUser['role']] ?? $currentUser['role'];
                        ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="stats-grid mb-4">
        <!-- مبيعات اليوم -->
        <div class="stat-card card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo formatCurrency($stats['daily_sales']['total_sales'] ?? 0); ?></h3>
                <p>مبيعات اليوم</p>
                <small><?php echo ($stats['daily_sales']['total_invoices'] ?? 0); ?> فاتورة</small>
            </div>
        </div>

        <!-- الصندوق -->
        <div class="stat-card card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #17a2b8, #6f42c1);">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo formatCurrency($stats['cash']['total_cash'] ?? 0); ?></h3>
                <p>رصيد الصناديق</p>
                <small>نقدي متاح</small>
            </div>
        </div>

        <!-- المخزون -->
        <div class="stat-card card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo ($stats['inventory']['total_products'] ?? 0); ?></h3>
                <p>إجمالي المنتجات</p>
                <small style="color: <?php echo ($stats['inventory']['low_stock_products'] ?? 0) > 0 ? '#dc3545' : '#28a745'; ?>;">
                    <?php echo ($stats['inventory']['low_stock_products'] ?? 0); ?> منتج ناقص
                </small>
            </div>
        </div>

        <!-- العملاء -->
        <div class="stat-card card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #dc3545, #e83e8c);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo ($stats['customers']['total_customers'] ?? 0); ?></h3>
                <p>إجمالي العملاء</p>
                <small><?php echo formatCurrency($stats['customers']['total_receivables'] ?? 0); ?> مستحق</small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الوحدات السريعة -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-th-large"></i> الوحدات الرئيسية</h4>
                </div>
                <div class="card-body">
                    <div class="modules-grid">
                        <!-- المبيعات -->
                        <a href="?page=sales" class="module-card">
                            <div class="module-icon" style="background: #28a745;">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h5>المبيعات</h5>
                            <p>إدارة الفواتير والمبيعات</p>
                        </a>

                        <!-- المشتريات -->
                        <a href="?page=purchases" class="module-card">
                            <div class="module-icon" style="background: #17a2b8;">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <h5>المشتريات</h5>
                            <p>إدارة فواتير المشتريات</p>
                        </a>

                        <!-- المخزون -->
                        <a href="?page=inventory" class="module-card">
                            <div class="module-icon" style="background: #ffc107;">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <h5>المخزون</h5>
                            <p>إدارة المنتجات والمخزون</p>
                        </a>

                        <!-- المحاسبة -->
                        <a href="?page=accounting" class="module-card">
                            <div class="module-icon" style="background: #6f42c1;">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <h5>المحاسبة</h5>
                            <p>القيود والحسابات</p>
                        </a>

                        <!-- التقارير -->
                        <a href="?page=reports" class="module-card">
                            <div class="module-icon" style="background: #dc3545;">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h5>التقارير</h5>
                            <p>التقارير المالية</p>
                        </a>

                        <!-- الإعدادات -->
                        <a href="?page=settings" class="module-card">
                            <div class="module-icon" style="background: #6c757d;">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h5>الإعدادات</h5>
                            <p>إعدادات النظام</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- النشاطات والتنبيهات -->
        <div class="col-md-4">
            <!-- تنبيهات المخزون -->
            <?php if (!empty($stats['low_stock_products'])): ?>
            <div class="card mb-3">
                <div class="card-header">
                    <h5 style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($stats['low_stock_products'] as $product): ?>
                    <div class="alert-item">
                        <strong><?php echo htmlspecialchars($product['name']); ?></strong><br>
                        <small>الكمية: <?php echo $product['stock_quantity']; ?> | الحد الأدنى: <?php echo $product['min_stock_level']; ?></small>
                    </div>
                    <?php endforeach; ?>
                    <a href="?page=inventory&action=low_stock" class="btn btn-sm btn-warning w-100 mt-2">
                        عرض جميع المنتجات الناقصة
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <!-- النشاطات الأخيرة -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> النشاطات الأخيرة</h5>
                </div>
                <div class="card-body">
                    <div class="activity-list">
                        <?php foreach ($recentActivities as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-content">
                                <strong><?php echo htmlspecialchars($activity['full_name'] ?? 'مستخدم'); ?></strong>
                                <p><?php echo htmlspecialchars($activity['action']); ?></p>
                                <small><?php echo formatDate($activity['created_at']); ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* أنماط لوحة التحكم */
.dashboard-container {
    padding: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border: none;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-left: 1rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

.stat-content p {
    margin: 0.25rem 0;
    color: #666;
    font-weight: 600;
}

.stat-content small {
    color: #999;
    font-size: 0.85rem;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.module-card {
    display: block;
    padding: 1.5rem;
    background: white;
    border: 2px solid #f8f9fa;
    border-radius: 1rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    text-align: center;
}

.module-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
}

.module-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin: 0 auto 1rem;
}

.module-card h5 {
    margin: 0 0 0.5rem;
    font-weight: 600;
    color: #333;
}

.module-card p {
    margin: 0;
    font-size: 0.9rem;
    color: #666;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.alert-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.alert-item:last-child {
    border-bottom: none;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.85rem;
    font-weight: 600;
}

.badge-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.badge-secondary {
    background: #6c757d;
    color: white;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
    padding: 0 0.75rem;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 0.75rem;
}

@media (max-width: 768px) {
    .col-md-8, .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}
</style>

<script>
// تحديث الوقت كل دقيقة
setInterval(function() {
    const now = new Date();
    const timeElement = document.querySelector('.fa-clock').parentElement;
    if (timeElement) {
        const timeText = timeElement.textContent.split('|')[1];
        timeElement.innerHTML = timeElement.innerHTML.replace(/\d{2}:\d{2}/, now.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}));
    }
}, 60000);

// تأثيرات الحركة
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.stat-card, .module-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

console.log('📊 لوحة التحكم جاهزة');
</script>

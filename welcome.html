<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك في نظام إدارة المخبز المحاسبي</title>

    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- خط Cairo العربي -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
        }

        /* الحاوي الرئيسي */
        .welcome-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
            position: relative;
        }

        /* الخلفية المتحركة */
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .floating-shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape-3 {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* بطاقة الترحيب */
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            animation: slideInUp 1s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* الشعار */
        .logo {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* العنوان */
        .welcome-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* الوصف */
        .welcome-description {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        /* الأزرار */
        .buttons-container {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            border-radius: 50px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            min-width: 200px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(40, 167, 69, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
            transform: translateY(-3px);
        }

        /* أدوات التطوير */
        .dev-tools-section {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px dashed #ffc107;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: center;
        }

        .dev-tools-section h4 {
            color: #856404;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .dev-note {
            color: #856404;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            font-style: italic;
        }

        .dev-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .btn-dev {
            background: linear-gradient(135deg, #ffc107, #ffb300);
            color: #212529;
            font-size: 0.9rem;
            padding: 0.75rem 1rem;
            border: 1px solid #ffc107;
        }

        .btn-dev:hover {
            background: linear-gradient(135deg, #ffb300, #ff8f00);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(255, 193, 7, 0.3);
        }

        .sync-status {
            background: rgba(255, 255, 255, 0.7);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            color: #856404;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sync-status.active {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            animation: pulse-green 2s infinite;
        }

        .sync-status.error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        @keyframes pulse-green {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* معلومات النظام */
        .system-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            border-right: 4px solid #667eea;
        }

        .system-info h4 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.95rem;
        }

        .feature-item i {
            color: #28a745;
            font-size: 1.1rem;
        }

        /* حالة الاتصال */
        .connection-status {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .status-online {
            color: #28a745;
        }

        .status-offline {
            color: #dc3545;
        }

        /* التذييل */
        .footer {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            text-align: center;
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .welcome-card {
                padding: 2rem;
                margin: 1rem;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .logo {
                font-size: 3rem;
            }

            .buttons-container {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* تأثيرات إضافية */
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
            animation: sparkle 2s linear infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }

        /* شاشة التحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(102, 126, 234, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- الخلفية المتحركة -->
    <div class="background-animation">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
    </div>

    <!-- حالة الاتصال -->
    <div class="connection-status" id="connectionStatus">
        <i class="fas fa-wifi"></i>
        <span id="statusText">جاري فحص الاتصال...</span>
    </div>

    <!-- شاشة التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>جاري تحميل النظام...</h3>
            <p>يرجى الانتظار</p>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="welcome-container">
        <div class="welcome-card">
            <!-- الشعار -->
            <div class="logo">
                <i class="fas fa-bread-slice"></i>
            </div>

            <!-- العنوان -->
            <h1 class="welcome-title">مرحباً بك في نظام إدارة المخبز</h1>

            <!-- الوصف -->
            <p class="welcome-description">
                النظام المحاسبي المتكامل لإدارة جميع عمليات المخبز<br>
                محاسبة • مخزون • مبيعات • موظفين • تقارير
            </p>

            <!-- الأزرار الرئيسية -->
            <div class="buttons-container">
                <button class="btn btn-primary" onclick="openLaragonSystem()">
                    <i class="fas fa-rocket"></i>
                    دخول النظام الكامل
                </button>

                <button class="btn btn-secondary" onclick="openOfflineMode()">
                    <i class="fas fa-laptop"></i>
                    الوضع المحلي
                </button>

                <button class="btn btn-outline" onclick="checkSystem()">
                    <i class="fas fa-stethoscope"></i>
                    فحص النظام
                </button>
            </div>

            <!-- أدوات التطوير -->
            <div class="dev-tools-section">
                <h4><i class="fas fa-tools"></i> أدوات التطوير (مؤقتة)</h4>
                <p class="dev-note">هذه الأدوات للتطوير فقط - سيتم إزالتها عند اكتمال المشروع</p>

                <div class="dev-buttons">
                    <button class="btn btn-dev" onclick="runCopyToLaragon()">
                        <i class="fas fa-copy"></i>
                        نسخ للارجون (مرة واحدة)
                    </button>

                    <button class="btn btn-dev" onclick="startAutoSync()">
                        <i class="fas fa-sync-alt"></i>
                        مزامنة تلقائية
                    </button>

                    <button class="btn btn-dev" onclick="startFileWatcher()">
                        <i class="fas fa-eye"></i>
                        مراقب الملفات
                    </button>

                    <button class="btn btn-dev" onclick="openDevTools()">
                        <i class="fas fa-wrench"></i>
                        أدوات المطور
                    </button>

                    <button class="btn btn-dev" onclick="openDatabaseMonitor()">
                        <i class="fas fa-database"></i>
                        مراقب قاعدة البيانات
                    </button>

                    <button class="btn btn-dev" onclick="createBackup()">
                        <i class="fas fa-download"></i>
                        نسخة احتياطية
                    </button>
                </div>

                <div class="sync-status" id="syncStatus">
                    <i class="fas fa-info-circle"></i>
                    <span>جاهز للمزامنة</span>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="system-info">
                <h4><i class="fas fa-star"></i> مميزات النظام</h4>
                <div class="features-grid">
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>محاسبة متكاملة</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>إدارة المخزون</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>نظام الفواتير</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>إدارة الموظفين</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>التقارير المالية</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>يعمل أوفلاين وأونلاين</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التذييل -->
    <div class="footer">
        <p>© 2024 أنوار سوفت - جميع الحقوق محفوظة</p>
    </div>

    <script>
        // إعدادات النظام
        const SYSTEM_CONFIG = {
            laragonUrl: 'http://localhost/bakery-system/',
            offlineUrl: 'offline-app.html',
            checkUrl: 'http://localhost/bakery-system/check-system.php'
        };

        // فحص حالة الاتصال
        async function checkConnection() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');

            try {
                // محاولة الوصول لـ Laragon
                const response = await fetch(SYSTEM_CONFIG.laragonUrl, {
                    method: 'HEAD',
                    mode: 'no-cors',
                    cache: 'no-cache'
                });

                statusElement.className = 'connection-status status-online';
                statusText.innerHTML = '<i class="fas fa-check-circle"></i> Laragon متصل';
                return true;
            } catch (error) {
                statusElement.className = 'connection-status status-offline';
                statusText.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Laragon غير متصل';
                return false;
            }
        }

        // فتح النظام الكامل في Laragon
        async function openLaragonSystem() {
            showLoading();

            const isConnected = await checkConnection();

            if (isConnected) {
                // فتح النظام في نافذة جديدة
                window.open(SYSTEM_CONFIG.laragonUrl, '_blank');
                hideLoading();
            } else {
                hideLoading();

                // عرض خيارات بديلة
                const choice = confirm(
                    'لا يمكن الوصول إلى Laragon.\n\n' +
                    'هل تريد:\n' +
                    '• موافق: تشغيل Laragon أولاً\n' +
                    '• إلغاء: استخدام الوضع المحلي'
                );

                if (choice) {
                    alert(
                        'يرجى تشغيل Laragon أولاً:\n\n' +
                        '1. افتح Laragon\n' +
                        '2. اضغط "Start All"\n' +
                        '3. عد وجرب مرة أخرى'
                    );
                } else {
                    openOfflineMode();
                }
            }
        }

        // فتح الوضع المحلي
        function openOfflineMode() {
            showLoading();

            setTimeout(() => {
                window.open(SYSTEM_CONFIG.offlineUrl, '_blank');
                hideLoading();
            }, 1000);
        }

        // فحص النظام
        async function checkSystem() {
            showLoading();

            try {
                window.open(SYSTEM_CONFIG.checkUrl, '_blank');
            } catch (error) {
                alert('لا يمكن فتح أداة فحص النظام. تأكد من تشغيل Laragon.');
            }

            hideLoading();
        }

        // === أدوات التطوير ===

        // نسخ للارجون (مرة واحدة)
        function runCopyToLaragon() {
            updateSyncStatus('جاري النسخ...', 'active');

            try {
                // محاولة تشغيل ملف النسخ
                const link = document.createElement('a');
                link.href = 'copy-to-laragon.bat';
                link.download = 'copy-to-laragon.bat';
                link.click();

                setTimeout(() => {
                    updateSyncStatus('تم تحميل ملف النسخ - شغله من مجلد التحميلات', 'active');

                    // إرشادات للمستخدم
                    alert(
                        'تم تحميل ملف النسخ!\n\n' +
                        'الخطوات:\n' +
                        '1. اذهب لمجلد التحميلات\n' +
                        '2. انقر مرتين على copy-to-laragon.bat\n' +
                        '3. اتبع التعليمات'
                    );
                }, 1000);

            } catch (error) {
                updateSyncStatus('فشل في النسخ', 'error');
                alert('حدث خطأ في النسخ. جرب النسخ اليدوي.');
            }
        }

        // بدء المزامنة التلقائية
        function startAutoSync() {
            updateSyncStatus('جاري تشغيل المزامنة التلقائية...', 'active');

            try {
                // محاولة تشغيل ملف المزامنة التلقائية
                const link = document.createElement('a');
                link.href = 'auto-sync.bat';
                link.download = 'auto-sync.bat';
                link.click();

                setTimeout(() => {
                    updateSyncStatus('تم تحميل ملف المزامنة التلقائية', 'active');

                    alert(
                        'تم تحميل ملف المزامنة التلقائية!\n\n' +
                        'الخطوات:\n' +
                        '1. اذهب لمجلد التحميلات\n' +
                        '2. انقر مرتين على auto-sync.bat\n' +
                        '3. اتركه يعمل في الخلفية'
                    );
                }, 1000);

            } catch (error) {
                updateSyncStatus('فشل في تشغيل المزامنة', 'error');
                alert('حدث خطأ في تشغيل المزامنة التلقائية.');
            }
        }

        // بدء مراقب الملفات
        function startFileWatcher() {
            updateSyncStatus('جاري تشغيل مراقب الملفات...', 'active');

            try {
                const link = document.createElement('a');
                link.href = 'watch-and-sync.bat';
                link.download = 'watch-and-sync.bat';
                link.click();

                setTimeout(() => {
                    updateSyncStatus('تم تحميل مراقب الملفات', 'active');

                    alert(
                        'تم تحميل مراقب الملفات!\n\n' +
                        'الخطوات:\n' +
                        '1. اذهب لمجلد التحميلات\n' +
                        '2. انقر مرتين على watch-and-sync.bat\n' +
                        '3. سيراقب التغييرات وينسخها فوراً'
                    );
                }, 1000);

            } catch (error) {
                updateSyncStatus('فشل في تشغيل مراقب الملفات', 'error');
                alert('حدث خطأ في تشغيل مراقب الملفات.');
            }
        }

        // فتح أدوات المطور
        function openDevTools() {
            showLoading();

            try {
                window.open('http://localhost/bakery-system/dev-tools.php', '_blank');
                updateSyncStatus('تم فتح أدوات المطور', 'active');
            } catch (error) {
                updateSyncStatus('فشل في فتح أدوات المطور', 'error');
                alert('لا يمكن فتح أدوات المطور. تأكد من تشغيل Laragon.');
            }

            hideLoading();
        }

        // فتح مراقب قاعدة البيانات
        function openDatabaseMonitor() {
            showLoading();

            try {
                window.open('http://localhost/bakery-system/database-monitor.php', '_blank');
                updateSyncStatus('تم فتح مراقب قاعدة البيانات', 'active');
            } catch (error) {
                updateSyncStatus('فشل في فتح مراقب قاعدة البيانات', 'error');
                alert('لا يمكن فتح مراقب قاعدة البيانات. تأكد من تشغيل Laragon.');
            }

            hideLoading();
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            updateSyncStatus('جاري إنشاء نسخة احتياطية...', 'active');

            try {
                // إنشاء نسخة احتياطية من الملفات الحالية
                const backupData = {
                    timestamp: new Date().toISOString(),
                    files: [],
                    version: '1.0.0'
                };

                // تحويل البيانات إلى JSON
                const dataStr = JSON.stringify(backupData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                // تحميل النسخة الاحتياطية
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `bakery-backup-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                URL.revokeObjectURL(url);

                updateSyncStatus('تم إنشاء النسخة الاحتياطية', 'active');

                setTimeout(() => {
                    alert('تم إنشاء نسخة احتياطية وتحميلها في مجلد التحميلات!');
                }, 1000);

            } catch (error) {
                updateSyncStatus('فشل في إنشاء النسخة الاحتياطية', 'error');
                alert('حدث خطأ في إنشاء النسخة الاحتياطية.');
            }
        }

        // تحديث حالة المزامنة
        function updateSyncStatus(message, status = '') {
            const statusElement = document.getElementById('syncStatus');
            const iconElement = statusElement.querySelector('i');
            const textElement = statusElement.querySelector('span');

            // تحديث النص
            textElement.textContent = message;

            // تحديث الحالة والأيقونة
            statusElement.className = `sync-status ${status}`;

            switch(status) {
                case 'active':
                    iconElement.className = 'fas fa-sync-alt fa-spin';
                    break;
                case 'error':
                    iconElement.className = 'fas fa-exclamation-triangle';
                    break;
                default:
                    iconElement.className = 'fas fa-info-circle';
            }

            // إعادة تعيين الحالة بعد 5 ثوان
            if (status !== '') {
                setTimeout(() => {
                    updateSyncStatus('جاهز للمزامنة', '');
                }, 5000);
            }
        }

        // إظهار شاشة التحميل
        function showLoading() {
            document.getElementById('loadingOverlay').classList.add('show');
        }

        // إخفاء شاشة التحميل
        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('show');
        }

        // إضافة تأثيرات بصرية
        function createSparkles() {
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    const sparkle = document.createElement('div');
                    sparkle.className = 'sparkle';
                    sparkle.style.left = Math.random() * 100 + '%';
                    sparkle.style.top = Math.random() * 100 + '%';
                    sparkle.style.animationDelay = Math.random() * 2 + 's';

                    document.body.appendChild(sparkle);

                    setTimeout(() => {
                        sparkle.remove();
                    }, 2000);
                }, i * 200);
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // فحص الاتصال عند التحميل
            checkConnection();

            // فحص دوري كل 30 ثانية
            setInterval(checkConnection, 30000);

            // إضافة تأثيرات بصرية
            setInterval(createSparkles, 5000);

            // اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            openLaragonSystem();
                            break;
                        case '2':
                            e.preventDefault();
                            openOfflineMode();
                            break;
                        case '3':
                            e.preventDefault();
                            checkSystem();
                            break;
                        // أدوات التطوير
                        case '4':
                            e.preventDefault();
                            runCopyToLaragon();
                            break;
                        case '5':
                            e.preventDefault();
                            startAutoSync();
                            break;
                        case '6':
                            e.preventDefault();
                            startFileWatcher();
                            break;
                        case '7':
                            e.preventDefault();
                            openDevTools();
                            break;
                        case '8':
                            e.preventDefault();
                            openDatabaseMonitor();
                            break;
                        case '9':
                            e.preventDefault();
                            createBackup();
                            break;
                    }
                }

                // اختصارات خاصة لأدوات التطوير
                if (e.ctrlKey && e.shiftKey) {
                    switch(e.key) {
                        case 'S':
                            e.preventDefault();
                            startAutoSync();
                            break;
                        case 'W':
                            e.preventDefault();
                            startFileWatcher();
                            break;
                        case 'D':
                            e.preventDefault();
                            openDevTools();
                            break;
                        case 'B':
                            e.preventDefault();
                            createBackup();
                            break;
                    }
                }
            });

            console.log('🍞 صفحة الترحيب جاهزة!');
            console.log('⌨️ اختصارات أساسية:');
            console.log('   Ctrl+1: النظام الكامل');
            console.log('   Ctrl+2: الوضع المحلي');
            console.log('   Ctrl+3: فحص النظام');
            console.log('🛠️ اختصارات التطوير:');
            console.log('   Ctrl+4: نسخ للارجون');
            console.log('   Ctrl+5: مزامنة تلقائية');
            console.log('   Ctrl+6: مراقب الملفات');
            console.log('   Ctrl+7: أدوات المطور');
            console.log('   Ctrl+8: مراقب قاعدة البيانات');
            console.log('   Ctrl+9: نسخة احتياطية');
            console.log('⚡ اختصارات سريعة:');
            console.log('   Ctrl+Shift+S: مزامنة تلقائية');
            console.log('   Ctrl+Shift+W: مراقب الملفات');
            console.log('   Ctrl+Shift+D: أدوات المطور');
            console.log('   Ctrl+Shift+B: نسخة احتياطية');
        });

        // معالجة الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ في الصفحة:', e.error);
        });

        // كشف حالة الشبكة
        window.addEventListener('online', function() {
            checkConnection();
        });

        window.addEventListener('offline', function() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            statusElement.className = 'connection-status status-offline';
            statusText.innerHTML = '<i class="fas fa-wifi-slash"></i> لا يوجد إنترنت';
        });
    </script>
</body>
</html>

-- البيانات الأولية للنظام المحاسبي
-- Initial Data for Bakery Accounting System

-- ===================================
-- إدراج بيانات المنشأة الافتراضية
-- ===================================
INSERT INTO company_settings (
    company_name, company_name_en, address, phone, mobile, email,
    currency_code, currency_name, currency_symbol, decimal_places, tax_rate,
    fiscal_year_start
) VALUES (
    'مخبز أنوار', 'Anwar Bakery', 'صنعاء - اليمن', '01-123456', '*********', '<EMAIL>',
    'YER', 'ريال يمني', 'ر.ي', 2, 0.00, '2024-01-01'
);

-- ===================================
-- إعدادات النظام الافتراضية
-- ===================================
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('app_name', 'نظام إدارة المخبز', 'string', 'اسم التطبيق'),
('app_version', '1.0.0', 'string', 'إصدار التطبيق'),
('language', 'ar', 'string', 'لغة النظام'),
('timezone', 'Asia/Aden', 'string', 'المنطقة الزمنية'),
('date_format', 'Y-m-d', 'string', 'تنسيق التاريخ'),
('time_format', 'H:i:s', 'string', 'تنسيق الوقت'),
('auto_backup', 'true', 'boolean', 'النسخ الاحتياطي التلقائي'),
('backup_frequency', 'daily', 'string', 'تكرار النسخ الاحتياطي'),
('print_format', 'thermal', 'string', 'تنسيق الطباعة'),
('invoice_prefix', 'INV', 'string', 'بادئة رقم الفاتورة'),
('voucher_prefix', 'VOU', 'string', 'بادئة رقم السند'),
('journal_prefix', 'JE', 'string', 'بادئة رقم القيد');

-- ===================================
-- إنشاء المستخدم الافتراضي (المدير)
-- ===================================
INSERT INTO users (
    username, password, full_name, email, role, permissions, is_active
) VALUES (
    'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'مدير النظام', '<EMAIL>', 'admin', 
    '{"all": true}', TRUE
);

-- ===================================
-- وحدات القياس الأساسية
-- ===================================
INSERT INTO units (unit_name, unit_symbol, created_by) VALUES
('كيلوجرام', 'كجم', 1),
('جرام', 'جم', 1),
('لتر', 'لتر', 1),
('مليلتر', 'مل', 1),
('قطعة', 'قطعة', 1),
('كيس', 'كيس', 1),
('علبة', 'علبة', 1),
('صندوق', 'صندوق', 1),
('متر', 'م', 1),
('سنتيمتر', 'سم', 1);

-- ===================================
-- شجرة الحسابات الأساسية
-- ===================================

-- الحسابات الرئيسية
INSERT INTO chart_of_accounts (account_code, account_name, account_type, account_level, is_main, created_by) VALUES
('1', 'الأصول', 'asset', 1, TRUE, 1),
('2', 'الخصوم', 'liability', 1, TRUE, 1),
('3', 'حقوق الملكية', 'equity', 1, TRUE, 1),
('4', 'الإيرادات', 'revenue', 1, TRUE, 1),
('5', 'المصروفات', 'expense', 1, TRUE, 1);

-- الأصول المتداولة
INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by) VALUES
('11', 'الأصول المتداولة', 1, 'asset', 2, 1),
('111', 'النقدية والبنوك', (SELECT id FROM chart_of_accounts WHERE account_code = '11'), 'asset', 3, 1),
('1111', 'الصناديق', (SELECT id FROM chart_of_accounts WHERE account_code = '111'), 'asset', 4, 1),
('1112', 'البنوك', (SELECT id FROM chart_of_accounts WHERE account_code = '111'), 'asset', 4, 1),
('112', 'العملاء', (SELECT id FROM chart_of_accounts WHERE account_code = '11'), 'asset', 3, 1),
('113', 'المخزون', (SELECT id FROM chart_of_accounts WHERE account_code = '11'), 'asset', 3, 1),
('1131', 'مخزون الخامات', (SELECT id FROM chart_of_accounts WHERE account_code = '113'), 'asset', 4, 1),
('1132', 'مخزون المنتجات', (SELECT id FROM chart_of_accounts WHERE account_code = '113'), 'asset', 4, 1),
('114', 'المصروفات المدفوعة مقدماً', (SELECT id FROM chart_of_accounts WHERE account_code = '11'), 'asset', 3, 1);

-- الأصول الثابتة
INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by) VALUES
('12', 'الأصول الثابتة', 1, 'asset', 2, 1),
('121', 'الأراضي والمباني', (SELECT id FROM chart_of_accounts WHERE account_code = '12'), 'asset', 3, 1),
('122', 'المعدات والآلات', (SELECT id FROM chart_of_accounts WHERE account_code = '12'), 'asset', 3, 1),
('123', 'الأثاث والتجهيزات', (SELECT id FROM chart_of_accounts WHERE account_code = '12'), 'asset', 3, 1),
('124', 'وسائل النقل', (SELECT id FROM chart_of_accounts WHERE account_code = '12'), 'asset', 3, 1),
('125', 'مجمع الإهلاك', (SELECT id FROM chart_of_accounts WHERE account_code = '12'), 'asset', 3, 1);

-- الخصوم المتداولة
INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by) VALUES
('21', 'الخصوم المتداولة', 2, 'liability', 2, 1),
('211', 'الموردون', (SELECT id FROM chart_of_accounts WHERE account_code = '21'), 'liability', 3, 1),
('212', 'رواتب الموظفين', (SELECT id FROM chart_of_accounts WHERE account_code = '21'), 'liability', 3, 1),
('213', 'الضرائب المستحقة', (SELECT id FROM chart_of_accounts WHERE account_code = '21'), 'liability', 3, 1),
('214', 'المصروفات المستحقة', (SELECT id FROM chart_of_accounts WHERE account_code = '21'), 'liability', 3, 1);

-- الخصوم طويلة الأجل
INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by) VALUES
('22', 'الخصوم طويلة الأجل', 2, 'liability', 2, 1),
('221', 'القروض طويلة الأجل', (SELECT id FROM chart_of_accounts WHERE account_code = '22'), 'liability', 3, 1);

-- حقوق الملكية
INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by) VALUES
('31', 'رأس المال', 3, 'equity', 2, 1),
('32', 'الأرباح المحتجزة', 3, 'equity', 2, 1),
('33', 'أرباح السنة الحالية', 3, 'equity', 2, 1);

-- الإيرادات
INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by) VALUES
('41', 'إيرادات المبيعات', 4, 'revenue', 2, 1),
('411', 'مبيعات المخبوزات', (SELECT id FROM chart_of_accounts WHERE account_code = '41'), 'revenue', 3, 1),
('412', 'مبيعات الحلويات', (SELECT id FROM chart_of_accounts WHERE account_code = '41'), 'revenue', 3, 1),
('413', 'مبيعات المشروبات', (SELECT id FROM chart_of_accounts WHERE account_code = '41'), 'revenue', 3, 1),
('42', 'إيرادات أخرى', 4, 'revenue', 2, 1),
('421', 'إيرادات الخدمات', (SELECT id FROM chart_of_accounts WHERE account_code = '42'), 'revenue', 3, 1);

-- المصروفات
INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by) VALUES
('51', 'تكلفة البضاعة المباعة', 5, 'expense', 2, 1),
('511', 'تكلفة الخامات', (SELECT id FROM chart_of_accounts WHERE account_code = '51'), 'expense', 3, 1),
('512', 'تكلفة العمالة المباشرة', (SELECT id FROM chart_of_accounts WHERE account_code = '51'), 'expense', 3, 1),
('52', 'المصروفات التشغيلية', 5, 'expense', 2, 1),
('521', 'رواتب الموظفين', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('522', 'الإيجار', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('523', 'الكهرباء والماء', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('524', 'الهاتف والإنترنت', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('525', 'الصيانة والإصلاح', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('526', 'مصروفات النقل', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('527', 'مصروفات التسويق', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('528', 'مصروفات إدارية أخرى', (SELECT id FROM chart_of_accounts WHERE account_code = '52'), 'expense', 3, 1),
('53', 'مصروفات الإهلاك', 5, 'expense', 2, 1),
('531', 'إهلاك المباني', (SELECT id FROM chart_of_accounts WHERE account_code = '53'), 'expense', 3, 1),
('532', 'إهلاك المعدات', (SELECT id FROM chart_of_accounts WHERE account_code = '53'), 'expense', 3, 1),
('533', 'إهلاك الأثاث', (SELECT id FROM chart_of_accounts WHERE account_code = '53'), 'expense', 3, 1);

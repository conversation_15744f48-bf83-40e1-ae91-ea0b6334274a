<?php
/**
 * صفحة تفاصيل الحساب وحركاته
 * Account Details and Movements Page
 */

// تضمين الملفات المطلوبة
require_once '../../config/database.php';
require_once '../../config/config.php';
require_once '../../includes/functions.php';

// التحقق من الصلاحيات
requirePermission('accounts', 'view');

$db = getDB();
$error = '';
$account_id = intval($_GET['id'] ?? 0);

if (!$account_id) {
    redirect('index.php');
}

try {
    // جلب بيانات الحساب
    $account = $db->selectOne(
        "SELECT a.*, p.account_name as parent_name 
         FROM chart_of_accounts a
         LEFT JOIN chart_of_accounts p ON a.parent_id = p.id
         WHERE a.id = ?",
        [$account_id]
    );
    
    if (!$account) {
        redirect('index.php');
    }
    
    // جلب حركات الحساب
    $movements = $db->select(
        "SELECT jed.*, je.entry_number, je.entry_date, je.description as entry_description, je.reference_type
         FROM journal_entry_details jed
         JOIN journal_entries je ON jed.entry_id = je.id
         WHERE jed.account_id = ?
         ORDER BY je.entry_date DESC, je.id DESC
         LIMIT 100",
        [$account_id]
    );
    
    // حساب الإحصائيات
    $stats = $db->selectOne(
        "SELECT 
            COUNT(*) as total_movements,
            COALESCE(SUM(debit_amount), 0) as total_debit,
            COALESCE(SUM(credit_amount), 0) as total_credit
         FROM journal_entry_details jed
         JOIN journal_entries je ON jed.entry_id = je.id
         WHERE jed.account_id = ?",
        [$account_id]
    );
    
    // جلب الحسابات الفرعية
    $sub_accounts = $db->select(
        "SELECT id, account_code, account_name, current_balance
         FROM chart_of_accounts
         WHERE parent_id = ?
         ORDER BY account_code",
        [$account_id]
    );
    
} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
}

$pageTitle = 'تفاصيل الحساب - ' . ($account['account_name'] ?? '');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .account-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .balance-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .balance-positive {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .balance-negative {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .balance-zero {
            background: linear-gradient(135deg, #6c757d, #adb5bd);
            color: white;
        }
        
        .movement-row:hover {
            background-color: #f8f9fa;
        }
        
        .debit-amount {
            color: #dc3545;
            font-weight: bold;
        }
        
        .credit-amount {
            color: #28a745;
            font-weight: bold;
        }
        
        .reference-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        
        .stats-card {
            border-left: 4px solid #007bff;
            padding: 1rem;
            background: #f8f9fa;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الهيدر -->
        <header class="header">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-file-alt"></i> <?php echo $pageTitle; ?></h1>
                <div>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة لشجرة الحسابات
                    </a>
                    <button onclick="window.print()" class="btn btn-info">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
        </header>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($account): ?>
        <!-- معلومات الحساب -->
        <div class="account-info">
            <div class="row">
                <div class="col-md-8">
                    <h2><i class="fas fa-chart-line"></i> <?php echo htmlspecialchars($account['account_name']); ?></h2>
                    <p><strong>رمز الحساب:</strong> <?php echo htmlspecialchars($account['account_code']); ?></p>
                    <p><strong>نوع الحساب:</strong> 
                        <?php
                        $types = [
                            'asset' => 'أصول',
                            'liability' => 'خصوم', 
                            'equity' => 'حقوق ملكية',
                            'revenue' => 'إيرادات',
                            'expense' => 'مصروفات'
                        ];
                        echo $types[$account['account_type']] ?? $account['account_type'];
                        ?>
                    </p>
                    <?php if ($account['parent_name']): ?>
                    <p><strong>الحساب الأب:</strong> <?php echo htmlspecialchars($account['parent_name']); ?></p>
                    <?php endif; ?>
                    <?php if ($account['description']): ?>
                    <p><strong>الوصف:</strong> <?php echo htmlspecialchars($account['description']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <?php
                    $balance = $account['current_balance'];
                    $balance_class = $balance > 0 ? 'balance-positive' : ($balance < 0 ? 'balance-negative' : 'balance-zero');
                    ?>
                    <div class="balance-card <?php echo $balance_class; ?>">
                        <h4>الرصيد الحالي</h4>
                        <h2><?php echo formatMoney(abs($balance)); ?></h2>
                        <p>
                            <?php if ($balance > 0): ?>
                                <i class="fas fa-arrow-up"></i> مدين
                            <?php elseif ($balance < 0): ?>
                                <i class="fas fa-arrow-down"></i> دائن
                            <?php else: ?>
                                <i class="fas fa-equals"></i> متوازن
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- إحصائيات الحساب -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> إحصائيات الحساب</h5>
                    </div>
                    <div class="card-body">
                        <div class="stats-card">
                            <h6><i class="fas fa-list"></i> إجمالي الحركات</h6>
                            <h4><?php echo number_format($stats['total_movements'] ?? 0); ?></h4>
                        </div>
                        
                        <div class="stats-card">
                            <h6><i class="fas fa-plus text-danger"></i> إجمالي المدين</h6>
                            <h4 class="debit-amount"><?php echo formatMoney($stats['total_debit'] ?? 0); ?></h4>
                        </div>
                        
                        <div class="stats-card">
                            <h6><i class="fas fa-minus text-success"></i> إجمالي الدائن</h6>
                            <h4 class="credit-amount"><?php echo formatMoney($stats['total_credit'] ?? 0); ?></h4>
                        </div>
                        
                        <div class="stats-card">
                            <h6><i class="fas fa-balance-scale"></i> الرصيد الافتتاحي</h6>
                            <h4><?php echo formatMoney($account['opening_balance']); ?></h4>
                        </div>
                    </div>
                </div>
                
                <!-- الحسابات الفرعية -->
                <?php if (!empty($sub_accounts)): ?>
                <div class="card mt-3">
                    <div class="card-header">
                        <h5><i class="fas fa-sitemap"></i> الحسابات الفرعية</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($sub_accounts as $sub): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                            <div>
                                <strong><?php echo htmlspecialchars($sub['account_name']); ?></strong><br>
                                <small class="text-muted"><?php echo htmlspecialchars($sub['account_code']); ?></small>
                            </div>
                            <div class="text-end">
                                <span class="<?php echo $sub['current_balance'] >= 0 ? 'debit-amount' : 'credit-amount'; ?>">
                                    <?php echo formatMoney(abs($sub['current_balance'])); ?>
                                </span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- حركات الحساب -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> آخر حركات الحساب</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($movements)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>رقم القيد</th>
                                        <th>البيان</th>
                                        <th>المدين</th>
                                        <th>الدائن</th>
                                        <th>النوع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($movements as $movement): ?>
                                    <tr class="movement-row">
                                        <td><?php echo formatDate($movement['entry_date']); ?></td>
                                        <td>
                                            <a href="../accounts/journal_entry.php?id=<?php echo $movement['entry_id']; ?>" 
                                               target="_blank" class="text-decoration-none">
                                                <?php echo htmlspecialchars($movement['entry_number']); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <div><?php echo htmlspecialchars($movement['entry_description']); ?></div>
                                            <?php if ($movement['description']): ?>
                                            <small class="text-muted"><?php echo htmlspecialchars($movement['description']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($movement['debit_amount'] > 0): ?>
                                            <span class="debit-amount"><?php echo formatMoney($movement['debit_amount']); ?></span>
                                            <?php else: ?>
                                            <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($movement['credit_amount'] > 0): ?>
                                            <span class="credit-amount"><?php echo formatMoney($movement['credit_amount']); ?></span>
                                            <?php else: ?>
                                            <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $reference_types = [
                                                'manual' => ['يدوي', 'secondary'],
                                                'invoice' => ['فاتورة', 'primary'],
                                                'voucher' => ['سند', 'info'],
                                                'salary' => ['راتب', 'warning'],
                                                'transfer' => ['تحويل', 'success'],
                                                'opening' => ['افتتاحي', 'dark'],
                                                'depreciation' => ['إهلاك', 'danger']
                                            ];
                                            $ref_info = $reference_types[$movement['reference_type']] ?? [$movement['reference_type'], 'secondary'];
                                            ?>
                                            <span class="badge bg-<?php echo $ref_info[1]; ?> reference-badge">
                                                <?php echo $ref_info[0]; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if (count($movements) >= 100): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض آخر 100 حركة فقط. لعرض جميع الحركات، يرجى استخدام التقارير.
                        </div>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد حركات لهذا الحساب</h5>
                            <p class="text-muted">لم يتم تسجيل أي قيود محاسبية لهذا الحساب بعد.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </main>

    <!-- JavaScript Files -->
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // تحديث الصفحة كل 30 ثانية للحصول على آخر البيانات
        setInterval(function() {
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
        
        // إضافة تأثيرات بصرية للحركات
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('.movement-row');
            rows.forEach((row, index) => {
                row.style.animationDelay = (index * 0.1) + 's';
                row.classList.add('fade-in');
            });
        });
    </script>
    
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        @media print {
            .header, .btn, .sidebar {
                display: none !important;
            }
            
            .main-content {
                margin-right: 0 !important;
            }
            
            .account-info {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 1px solid #ddd;
            }
            
            .balance-card {
                background: #f8f9fa !important;
                color: #333 !important;
                border: 1px solid #ddd;
            }
        }
    </style>
</body>
</html>

@echo off
chcp 65001 >nul
title نسخ ملفات المشروع إلى Laragon
color 0A

echo.
echo ========================================
echo     نسخ ملفات المشروع إلى Laragon
echo ========================================
echo.

REM تحديد المجلدات
set SOURCE_DIR=%~dp0
set TARGET_DIR=C:\laragon\www\bakery-system\

echo 📁 المجلد المصدر: %SOURCE_DIR%
echo 📁 المجلد الهدف: %TARGET_DIR%
echo.

REM التحقق من وجود Laragon
if not exist "C:\laragon\www\" (
    echo ❌ خطأ: لم يتم العثور على مجلد Laragon
    echo    يرجى التأكد من تثبيت Laragon في المسار الافتراضي
    echo    C:\laragon\www\
    pause
    exit /b 1
)

REM إنشاء مجلد المشروع إذا لم يكن موجوداً
if not exist "%TARGET_DIR%" (
    echo 📁 إنشاء مجلد المشروع...
    mkdir "%TARGET_DIR%"
    echo ✅ تم إنشاء المجلد: %TARGET_DIR%
) else (
    echo ✅ مجلد المشروع موجود: %TARGET_DIR%
)

echo.
echo 🔄 بدء نسخ الملفات...
echo.

REM نسخ جميع ملفات PHP
echo 📋 نسخ ملفات PHP...
for %%f in (*.php) do (
    copy "%%f" "%TARGET_DIR%" >nul 2>&1
    if exist "%TARGET_DIR%%%f" (
        echo ✅ تم نسخ: %%f
    ) else (
        echo ❌ فشل نسخ: %%f
    )
)

REM نسخ جميع ملفات HTML
echo.
echo 📋 نسخ ملفات HTML...
for %%f in (*.html) do (
    copy "%%f" "%TARGET_DIR%" >nul 2>&1
    if exist "%TARGET_DIR%%%f" (
        echo ✅ تم نسخ: %%f
    ) else (
        echo ❌ فشل نسخ: %%f
    )
)

REM نسخ جميع ملفات CSS
echo.
echo 📋 نسخ ملفات CSS...
for %%f in (*.css) do (
    copy "%%f" "%TARGET_DIR%" >nul 2>&1
    if exist "%TARGET_DIR%%%f" (
        echo ✅ تم نسخ: %%f
    ) else (
        echo ❌ فشل نسخ: %%f
    )
)

REM نسخ جميع ملفات JavaScript
echo.
echo 📋 نسخ ملفات JavaScript...
for %%f in (*.js) do (
    copy "%%f" "%TARGET_DIR%" >nul 2>&1
    if exist "%TARGET_DIR%%%f" (
        echo ✅ تم نسخ: %%f
    ) else (
        echo ❌ فشل نسخ: %%f
    )
)

REM نسخ ملفات أخرى مهمة
echo.
echo 📋 نسخ ملفات أخرى...
for %%f in (*.json *.txt *.md *.sql) do (
    if exist "%%f" (
        copy "%%f" "%TARGET_DIR%" >nul 2>&1
        if exist "%TARGET_DIR%%%f" (
            echo ✅ تم نسخ: %%f
        ) else (
            echo ❌ فشل نسخ: %%f
        )
    )
)

REM نسخ المجلدات المهمة (إذا وجدت)
if exist "assets" (
    echo.
    echo 📁 نسخ مجلد assets...
    xcopy "assets" "%TARGET_DIR%assets\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مجلد: assets
)

if exist "css" (
    echo 📁 نسخ مجلد css...
    xcopy "css" "%TARGET_DIR%css\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مجلد: css
)

if exist "js" (
    echo 📁 نسخ مجلد js...
    xcopy "js" "%TARGET_DIR%js\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مجلد: js
)

if exist "images" (
    echo 📁 نسخ مجلد images...
    xcopy "images" "%TARGET_DIR%images\" /E /I /Y >nul 2>&1
    echo ✅ تم نسخ مجلد: images
)

echo.
echo ========================================
echo            اكتمل النسخ!
echo ========================================
echo.

REM عرض ملخص
echo 📊 ملخص العملية:
echo    المجلد المصدر: %SOURCE_DIR%
echo    المجلد الهدف: %TARGET_DIR%
echo    التاريخ والوقت: %date% %time%
echo.

REM فتح المتصفح تلقائياً
echo 🌐 فتح التطبيق في المتصفح...
timeout /t 2 >nul
start http://localhost/bakery-system/laragon-app.php

echo.
echo ✅ تم النسخ بنجاح!
echo 🌐 تم فتح التطبيق في المتصفح
echo.
echo 💡 نصيحة: احفظ هذا الملف واستخدمه في كل مرة تريد نسخ التحديثات
echo.

REM إنشاء اختصار على سطح المكتب (اختياري)
choice /C YN /M "هل تريد إنشاء اختصار على سطح المكتب؟ (Y/N)"
if errorlevel 2 goto :end
if errorlevel 1 (
    echo 🔗 إنشاء اختصار على سطح المكتب...
    
    REM إنشاء ملف VBS لإنشاء الاختصار
    echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
    echo sLinkFile = "%USERPROFILE%\Desktop\نسخ المشروع إلى Laragon.lnk" >> CreateShortcut.vbs
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
    echo oLink.TargetPath = "%~f0" >> CreateShortcut.vbs
    echo oLink.WorkingDirectory = "%~dp0" >> CreateShortcut.vbs
    echo oLink.Description = "نسخ ملفات المشروع إلى Laragon" >> CreateShortcut.vbs
    echo oLink.Save >> CreateShortcut.vbs
    
    cscript CreateShortcut.vbs >nul 2>&1
    del CreateShortcut.vbs >nul 2>&1
    
    echo ✅ تم إنشاء الاختصار على سطح المكتب
)

:end
echo.
echo 🎉 شكراً لاستخدام أداة النسخ التلقائي!
echo 📞 للدعم الفني: أنوار سوفت
echo.
pause

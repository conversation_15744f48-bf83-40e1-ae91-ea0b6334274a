<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المخبز المحاسبي - أنوار سوفت</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* إضافات خاصة للنسخة الهجينة */
        .demo-banner {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: bold;
            margin-bottom: 1rem;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 2rem auto;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .form-group i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        
        .form-control {
            display: block;
            width: 100%;
            padding: 0.75rem 45px 0.75rem 15px;
            font-size: 1rem;
            border: 2px solid #f0f0f0;
            border-radius: 25px;
            height: 50px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .btn-login {
            width: 100%;
            height: 50px;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .form-check {
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .company-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #f0f0f0;
            color: #666;
        }
        
        .hidden {
            display: none;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <!-- بانر النسخة التجريبية -->
    <div class="demo-banner">
        <i class="fas fa-info-circle"></i>
        نسخة تجريبية - النظام المحاسبي الهجين HTML + JavaScript + LocalStorage
    </div>

    <!-- صفحة تسجيل الدخول -->
    <div id="loginPage" class="login-container">
        <!-- رأس صفحة تسجيل الدخول -->
        <div class="login-header">
            <h1><i class="fas fa-bread-slice"></i> نظام إدارة المخبز</h1>
            <p>النظام المحاسبي المتكامل</p>
        </div>
        
        <!-- محتوى صفحة تسجيل الدخول -->
        <div class="login-body">
            <form id="loginForm">
                <div class="form-group">
                    <i class="fas fa-user"></i>
                    <input type="text" 
                           id="username" 
                           class="form-control" 
                           placeholder="اسم المستخدم"
                           value="admin"
                           required>
                </div>
                
                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" 
                           id="password" 
                           class="form-control" 
                           placeholder="كلمة المرور"
                           value="password"
                           required>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" id="remember" checked>
                    <label for="remember">تذكرني لمدة 30 يوم</label>
                </div>
                
                <button type="submit" class="btn-login">
                    <span>تسجيل الدخول</span>
                </button>
            </form>
            
            <!-- معلومات الشركة -->
            <div class="company-info">
                <p><strong>بيانات تسجيل الدخول الافتراضية:</strong></p>
                <p>اسم المستخدم: <code>admin</code></p>
                <p>كلمة المرور: <code>password</code></p>
                <hr>
                <p><small>© 2024 أنوار سوفت. جميع الحقوق محفوظة.</small></p>
            </div>
        </div>
    </div>

    <!-- الصفحة الرئيسية (مخفية في البداية) -->
    <div id="mainApp" class="hidden">
        <!-- سيتم تحميل المحتوى هنا بواسطة JavaScript -->
    </div>

    <!-- JavaScript للنظام الهجين -->
    <script>
        // بيانات النظام المحفوظة محلياً
        const SYSTEM_DATA = {
            users: [
                {
                    id: 1,
                    username: 'admin',
                    password: 'password',
                    fullName: 'مدير النظام',
                    email: '<EMAIL>',
                    role: 'admin',
                    isActive: true
                }
            ],
            company: {
                name: 'مخبز أنوار',
                nameEn: 'Anwar Bakery',
                address: 'صنعاء - اليمن',
                phone: '01-123456',
                mobile: '*********',
                email: '<EMAIL>',
                currency: 'ريال يمني',
                currencySymbol: 'ر.ي'
            },
            accounts: [
                { id: 1, code: '1', name: 'الأصول', type: 'asset', level: 1, balance: 0 },
                { id: 2, code: '2', name: 'الخصوم', type: 'liability', level: 1, balance: 0 },
                { id: 3, code: '3', name: 'حقوق الملكية', type: 'equity', level: 1, balance: 0 },
                { id: 4, code: '4', name: 'الإيرادات', type: 'revenue', level: 1, balance: 0 },
                { id: 5, code: '5', name: 'المصروفات', type: 'expense', level: 1, balance: 0 },
                { id: 6, code: '11', name: 'الأصول المتداولة', parentId: 1, type: 'asset', level: 2, balance: 0 },
                { id: 7, code: '111', name: 'النقدية والبنوك', parentId: 6, type: 'asset', level: 3, balance: 50000 },
                { id: 8, code: '1111', name: 'الصناديق', parentId: 7, type: 'asset', level: 4, balance: 25000 },
                { id: 9, code: '1112', name: 'البنوك', parentId: 7, type: 'asset', level: 4, balance: 25000 }
            ],
            cashBoxes: [
                { id: 1, name: 'الصندوق الرئيسي', code: 'CASH001', balance: 15000, responsible: 'أحمد محمد' },
                { id: 2, name: 'صندوق المبيعات', code: 'CASH002', balance: 10000, responsible: 'فاطمة علي' }
            ],
            banks: [
                { id: 1, name: 'البنك الأهلي اليمني', accountNumber: '*********', accountName: 'مخبز أنوار', balance: 25000 }
            ],
            employees: [
                { id: 1, code: 'EMP001', name: 'أحمد محمد علي', jobTitle: 'أمين صندوق', salary: 80000, department: 'المبيعات', isActive: true },
                { id: 2, code: 'EMP002', name: 'فاطمة علي حسن', jobTitle: 'محاسبة', salary: 90000, department: 'المحاسبة', isActive: true },
                { id: 3, code: 'EMP003', name: 'محمد سالم', jobTitle: 'خباز رئيسي', salary: 85000, department: 'الإنتاج', isActive: true }
            ],
            stats: {
                todaySales: 45000,
                todayPurchases: 15000,
                totalCash: 25000,
                totalBank: 25000,
                totalEmployees: 3,
                activeEmployees: 3
            }
        };

        // حفظ البيانات في LocalStorage
        function saveData() {
            localStorage.setItem('bakerySystemData', JSON.stringify(SYSTEM_DATA));
        }

        // تحميل البيانات من LocalStorage
        function loadData() {
            const saved = localStorage.getItem('bakerySystemData');
            if (saved) {
                Object.assign(SYSTEM_DATA, JSON.parse(saved));
            }
        }

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // التحقق من بيانات المستخدم
            const user = SYSTEM_DATA.users.find(u => u.username === username && u.password === password);
            
            if (user) {
                // حفظ بيانات المستخدم الحالي
                sessionStorage.setItem('currentUser', JSON.stringify(user));
                
                // إخفاء صفحة تسجيل الدخول وإظهار التطبيق
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                
                // تحميل لوحة التحكم
                loadDashboard();
                
                // تغيير خلفية الصفحة
                document.body.style.background = '#f8f9fa';
                
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });

        // تحميل لوحة التحكم
        function loadDashboard() {
            const mainApp = document.getElementById('mainApp');
            mainApp.innerHTML = `
                <!-- الشريط الجانبي -->
                <nav class="sidebar">
                    <div class="sidebar-header">
                        <h3><i class="fas fa-bread-slice"></i> نظام إدارة المخبز</h3>
                        <p>مرحباً، ${JSON.parse(sessionStorage.getItem('currentUser')).fullName}</p>
                    </div>
                    
                    <ul class="sidebar-menu">
                        <li><a href="#" onclick="loadDashboard()" class="active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                        <li><a href="#" onclick="loadCompanySettings()"><i class="fas fa-building"></i> بيانات المنشأة</a></li>
                        <li><a href="#" onclick="loadAccounts()"><i class="fas fa-chart-line"></i> شجرة الحسابات</a></li>
                        <li><a href="#" onclick="loadUsers()"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                        <li><a href="#" onclick="loadCashBank()"><i class="fas fa-money-bill-wave"></i> الصناديق والبنوك</a></li>
                        <li><a href="#" onclick="loadEmployees()"><i class="fas fa-user-tie"></i> إدارة الموظفين</a></li>
                        <li><a href="#" onclick="loadInventory()"><i class="fas fa-boxes"></i> إدارة المخزون</a></li>
                        <li><a href="#" onclick="loadInvoices()"><i class="fas fa-file-invoice"></i> الفواتير</a></li>
                        <li><a href="#" onclick="loadVouchers()"><i class="fas fa-receipt"></i> السندات</a></li>
                        <li><a href="#" onclick="loadReports()"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                        <li><a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    </ul>
                </nav>

                <!-- المحتوى الرئيسي -->
                <main class="main-content">
                    <!-- الهيدر -->
                    <header class="header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                            <div class="current-datetime">${new Date().toLocaleDateString('ar-SA')}</div>
                        </div>
                    </header>

                    <!-- البطاقات الإحصائية -->
                    <div class="row mb-4">
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-shopping-cart fa-2x text-success mb-2"></i>
                                    <h5>مبيعات اليوم</h5>
                                    <h3 class="text-success">${formatMoney(SYSTEM_DATA.stats.todaySales)}</h3>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-shopping-bag fa-2x text-warning mb-2"></i>
                                    <h5>مشتريات اليوم</h5>
                                    <h3 class="text-warning">${formatMoney(SYSTEM_DATA.stats.todayPurchases)}</h3>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-cash-register fa-2x text-info mb-2"></i>
                                    <h5>إجمالي السيولة</h5>
                                    <h3 class="text-info">${formatMoney(SYSTEM_DATA.stats.totalCash + SYSTEM_DATA.stats.totalBank)}</h3>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h5>عدد الموظفين</h5>
                                    <h3 class="text-primary">${SYSTEM_DATA.stats.activeEmployees}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- أرصدة الصناديق -->
                        <div class="col-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-money-bill-wave"></i> أرصدة الصناديق</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>اسم الصندوق</th>
                                                <th>المسؤول</th>
                                                <th>الرصيد</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${SYSTEM_DATA.cashBoxes.map(box => `
                                                <tr>
                                                    <td>${box.name}</td>
                                                    <td>${box.responsible}</td>
                                                    <td>${formatMoney(box.balance)}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- الموظفين -->
                        <div class="col-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-user-tie"></i> الموظفين</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>اسم الموظف</th>
                                                <th>المسمى الوظيفي</th>
                                                <th>الراتب</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${SYSTEM_DATA.employees.map(emp => `
                                                <tr>
                                                    <td>${emp.name}</td>
                                                    <td>${emp.jobTitle}</td>
                                                    <td>${formatMoney(emp.salary)}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            `;
        }

        // دالة تنسيق المبلغ
        function formatMoney(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount) + ' ر.ي';
        }

        // دالة تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                sessionStorage.removeItem('currentUser');
                location.reload();
            }
        }

        // دوال تحميل الوحدات (ستكون بسيطة في النسخة التجريبية)
        function loadCompanySettings() {
            alert('وحدة بيانات المنشأة - قيد التطوير في النسخة الهجينة');
        }

        function loadAccounts() {
            alert('وحدة شجرة الحسابات - قيد التطوير في النسخة الهجينة');
        }

        function loadUsers() {
            alert('وحدة إدارة المستخدمين - قيد التطوير في النسخة الهجينة');
        }

        function loadCashBank() {
            alert('وحدة الصناديق والبنوك - قيد التطوير في النسخة الهجينة');
        }

        function loadEmployees() {
            alert('وحدة إدارة الموظفين - قيد التطوير في النسخة الهجينة');
        }

        function loadInventory() {
            alert('وحدة إدارة المخزون - قيد التطوير');
        }

        function loadInvoices() {
            alert('وحدة الفواتير - قيد التطوير');
        }

        function loadVouchers() {
            alert('وحدة السندات - قيد التطوير');
        }

        function loadReports() {
            alert('وحدة التقارير - قيد التطوير');
        }

        // تحميل البيانات عند بدء التطبيق
        loadData();
        
        // حفظ البيانات كل 30 ثانية
        setInterval(saveData, 30000);
    </script>
</body>
</html>

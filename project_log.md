# سجل تطوير النظام المحاسبي للمخبز

## معلومات المشروع
- **اسم المشروع**: نظام محاسبي شامل للمخبز
- **التقنيات**: HTML, CSS, JavaScript, PHP, MySQL
- **اللغة**: العربية مع دعم RTL كامل
- **العملة**: الريال اليمني

## الهيكل العام للمشروع
```
anwarsoft/
├── config/                 # ملفات الإعدادات
├── database/              # ملفات قاعدة البيانات
├── includes/              # ملفات PHP المشتركة
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات التنسيق
│   ├── js/               # ملفات JavaScript
│   └── images/           # الصور والأيقونات
├── modules/               # وحدات النظام
│   ├── company/          # بيانات المنشأة
│   ├── accounts/         # شجرة الحسابات
│   ├── users/            # إدارة المستخدمين
│   ├── cash_bank/        # الصناديق والبنوك
│   ├── employees/        # إدارة الموظفين
│   ├── inventory/        # إدارة المخزون
│   ├── invoices/         # الفواتير
│   ├── vouchers/         # السندات
│   ├── assets/           # الأصول والإهلاك
│   └── reports/          # التقارير
├── uploads/               # ملفات الرفع
└── index.php             # الصفحة الرئيسية
```

## سجل التطوير

### 2024-12-19 - بداية المشروع
- [x] إنشاء هيكل المجلدات الأساسي
- [x] إعداد قاعدة البيانات MySQL الشاملة
- [x] إنشاء ملفات الاتصال والإعدادات
- [x] تطوير نظام المصادقة الأساسي
- [x] إنشاء الصفحة الرئيسية ولوحة التحكم
- [x] تطوير نظام الصلاحيات والمستخدمين
- [x] إنشاء الشريط الجانبي التفاعلي
- [x] تطوير وحدة بيانات المنشأة
- [x] إنشاء ملف التثبيت التلقائي
- [x] إعداد ملف README شامل
- [x] تطوير نظام التحقق من البيانات
- [x] إنشاء ملفات CSS و JavaScript الأساسية

## الوحدات المطلوبة (حسب الأولوية)

### 1. بيانات النظام والمنشأة ⚙️
- [x] صفحة إعدادات المنشأة
- [ ] إعدادات النظام العامة
- [ ] نظام النسخ الاحتياطي
- [ ] إعادة ضبط المصنع

### 2. شجرة الحسابات المحاسبية 🧮
- [x] إنشاء الحسابات الرئيسية والفرعية
- [x] ربط الحسابات بقاعدة البيانات
- [x] واجهة إدخال القيود اليدوية
- [x] عرض أرصدة الحسابات
- [x] صفحة تفاصيل الحساب وحركاته

### 3. إدارة المستخدمين والصلاحيات 👤
- [x] نظام تسجيل الدخول
- [x] إدارة الصلاحيات المتقدمة
- [x] واجهة إدارة المستخدمين
- [ ] سجل تغييرات المستخدمين

### 4. إدارة الصناديق والبنوك 🏦
- [x] إنشاء الصناديق وربطها بالموظفين
- [x] إدارة الحسابات البنكية
- [x] واجهة عرض الأرصدة والإحصائيات
- [ ] تحويل المبالغ بين الصناديق

### 5. إدارة الموظفين والرواتب 🧍‍♂️
- [ ] بيانات الموظفين
- [ ] نظام الرواتب والاستحقاقات
- [ ] إدارة السلف والخصومات

### 6. إدارة الأصناف والمنتجات 📦
- [ ] إدارة الخامات والمنتجات
- [ ] وصفات المنتجات
- [ ] احتساب التكلفة والأسعار

### 7. نظام الفواتير 🧾
- [ ] فواتير المبيعات والمشتريات
- [ ] فواتير المرتجعات
- [ ] فاتورة البيع السريع

### 8. سندات القبض والصرف 💳
- [ ] سندات متعددة الجهات
- [ ] ربط تلقائي بالقيود المحاسبية

### 9. إدارة الأصول والإهلاك 🏢
- [ ] تسجيل الأصول
- [ ] حساب الإهلاك التلقائي

### 10. وحدة التقارير 📊
- [ ] تقارير الفواتير
- [ ] تقارير الصرف والقبض
- [ ] التقارير المحاسبية
- [ ] تصدير PDF وExcel

## ملاحظات مهمة
- جميع العمليات تنشئ قيود محاسبية تلقائية
- العملة الوحيدة: الريال اليمني
- دعم RTL كامل للغة العربية
- نظام صلاحيات متقدم
- ربط محكم بين جميع الوحدات

<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

// تضمين الملفات المطلوبة
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'config/security.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';
require_once 'includes/performance.php';

// إعادة التوجيه إذا كان المستخدم مسجل دخول بالفعل
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = cleanInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    // التحقق من صحة البيانات
    $validator = validator($_POST);
    $validator->required('username', 'اسم المستخدم مطلوب')
             ->required('password', 'كلمة المرور مطلوبة');
    
    if (!$validator->hasErrors()) {
        try {
            $db = getDB();
            
            // البحث عن المستخدم
            $user = $db->selectOne(
                "SELECT * FROM users WHERE username = ? AND is_active = 1",
                [$username]
            );
            
            if ($user && password_verify($password, $user['password'])) {
                // تسجيل الدخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['login_time'] = time();
                
                // تحديث آخر تسجيل دخول
                $db->update(
                    "UPDATE users SET last_login = NOW() WHERE id = ?",
                    [$user['id']]
                );
                
                // تسجيل النشاط
                logUserActivity('login', 'users', $user['id']);
                
                // إعداد كوكي التذكر
                if ($remember) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                    
                    // حفظ التوكن في قاعدة البيانات (يمكن إضافة جدول للتوكنات)
                }
                
                // إعادة التوجيه
                $redirect = $_GET['redirect'] ?? 'index.php';
                redirect($redirect);
                
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                
                // تسجيل محاولة دخول فاشلة
                logUserActivity('failed_login', null, null, null, [
                    'username' => $username,
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
                ]);
            }
            
        } catch (Exception $e) {
            $error = 'حدث خطأ في النظام. يرجى المحاولة لاحقاً';
            error_log("Login error: " . $e->getMessage());
        }
    } else {
        $error = $validator->getFirstError();
    }
}

$pageTitle = 'تسجيل الدخول';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .form-group i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        
        .form-control {
            padding-right: 45px;
            border: 2px solid #f0f0f0;
            border-radius: 25px;
            height: 50px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            width: 100%;
            height: 50px;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .form-check {
            margin: 1rem 0;
        }
        
        .alert {
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .company-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #f0f0f0;
            color: #666;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- رأس صفحة تسجيل الدخول -->
        <div class="login-header">
            <h1><i class="fas fa-bread-slice"></i> <?php echo APP_NAME; ?></h1>
            <p>نظام إدارة المخبز المتكامل</p>
        </div>
        
        <!-- محتوى صفحة تسجيل الدخول -->
        <div class="login-body">
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
            <?php endif; ?>
            
            <form method="POST" action="" id="loginForm">
                <div class="form-group">
                    <i class="fas fa-user"></i>
                    <input type="text" 
                           name="username" 
                           class="form-control" 
                           placeholder="اسم المستخدم"
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           required
                           autocomplete="username">
                </div>
                
                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" 
                           name="password" 
                           class="form-control" 
                           placeholder="كلمة المرور"
                           required
                           autocomplete="current-password">
                </div>
                
                <div class="form-check">
                    <input type="checkbox" 
                           name="remember" 
                           id="remember" 
                           class="form-check-input"
                           <?php echo isset($_POST['remember']) ? 'checked' : ''; ?>>
                    <label for="remember" class="form-check-label">
                        تذكرني لمدة 30 يوم
                    </label>
                </div>
                
                <button type="submit" class="btn btn-login">
                    <span class="login-text">تسجيل الدخول</span>
                    <span class="loading">
                        <i class="fas fa-spinner fa-spin"></i> جاري التحقق...
                    </span>
                </button>
            </form>
            
            <!-- معلومات الشركة -->
            <div class="company-info">
                <p><strong>بيانات تسجيل الدخول الافتراضية:</strong></p>
                <p>اسم المستخدم: <code>admin</code></p>
                <p>كلمة المرور: <code>password</code></p>
                <hr>
                <p><small>© 2024 أنوار سوفت. جميع الحقوق محفوظة.</small></p>
            </div>
        </div>
    </div>

    <script>
        // معالجة نموذج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const loginText = submitBtn.querySelector('.login-text');
            const loading = submitBtn.querySelector('.loading');
            
            // إظهار حالة التحميل
            loginText.style.display = 'none';
            loading.classList.add('show');
            submitBtn.disabled = true;
            
            // السماح بإرسال النموذج
            // (سيتم إعادة تحميل الصفحة وإخفاء التحميل تلقائياً)
        });
        
        // التركيز على حقل اسم المستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.querySelector('input[name="username"]');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        });
        
        // إخفاء التنبيهات تلقائياً بعد 5 ثوان
        document.querySelectorAll('.alert').forEach(function(alert) {
            setTimeout(function() {
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 300);
            }, 5000);
        });
    </script>
</body>
</html>

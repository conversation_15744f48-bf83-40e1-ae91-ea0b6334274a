-- بيانات تجريبية لنظام إدارة المخبز
-- Sample Data for Bakery Management System

USE bakery_system;

-- ===== إعدادات الشركة =====
INSERT INTO company_settings (
    company_name, company_name_en, address, phone, email, 
    tax_number, commercial_register, currency, currency_symbol
) VALUES (
    'مخبز الأنوار للمعجنات والحلويات',
    'Al-Anwar Bakery & Sweets',
    'شارع الزبيري، صنعاء، اليمن',
    '+967-1-234567',
    '<EMAIL>',
    '*********',
    'CR-2024-001',
    'YER',
    'ر.ي'
);

-- ===== المستخدمين =====
INSERT INTO users (username, password, full_name, email, phone, role, permissions, is_active) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد الإدارة', '<EMAIL>', '*********', 'admin', '["all"]', TRUE),
('accountant', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة علي المحاسبة', '<EMAIL>', '*********', 'accountant', '["accounting", "reports"]', TRUE),
('cashier1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد سالم الكاشير', '<EMAIL>', '*********', 'cashier', '["sales", "customers"]', TRUE),
('manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'سارة أحمد المدير', '<EMAIL>', '*********', 'manager', '["sales", "inventory", "reports"]', TRUE);

-- ===== شجرة الحسابات =====
-- الأصول
INSERT INTO chart_of_accounts (account_code, account_name, account_name_en, account_type, account_level, is_main_account) VALUES
('1', 'الأصول', 'Assets', 'asset', 1, TRUE),
('11', 'الأصول المتداولة', 'Current Assets', 'asset', 2, TRUE),
('111', 'النقدية والبنوك', 'Cash and Banks', 'asset', 3, TRUE),
('1111', 'الصندوق الرئيسي', 'Main Cash Box', 'asset', 4, FALSE),
('1112', 'البنك الأهلي', 'National Bank', 'asset', 4, FALSE),
('112', 'المخزون', 'Inventory', 'asset', 3, TRUE),
('1121', 'مخزون المواد الخام', 'Raw Materials Inventory', 'asset', 4, FALSE),
('1122', 'مخزون المنتجات الجاهزة', 'Finished Goods Inventory', 'asset', 4, FALSE),
('113', 'العملاء', 'Accounts Receivable', 'asset', 3, TRUE),
('1131', 'عملاء محليون', 'Local Customers', 'asset', 4, FALSE);

-- الخصوم
INSERT INTO chart_of_accounts (account_code, account_name, account_name_en, account_type, account_level, is_main_account) VALUES
('2', 'الخصوم', 'Liabilities', 'liability', 1, TRUE),
('21', 'الخصوم المتداولة', 'Current Liabilities', 'liability', 2, TRUE),
('211', 'الموردون', 'Accounts Payable', 'liability', 3, TRUE),
('2111', 'موردو المواد الخام', 'Raw Material Suppliers', 'liability', 4, FALSE),
('212', 'مصاريف مستحقة', 'Accrued Expenses', 'liability', 3, TRUE),
('2121', 'رواتب مستحقة', 'Accrued Salaries', 'liability', 4, FALSE);

-- حقوق الملكية
INSERT INTO chart_of_accounts (account_code, account_name, account_name_en, account_type, account_level, is_main_account) VALUES
('3', 'حقوق الملكية', 'Equity', 'equity', 1, TRUE),
('31', 'رأس المال', 'Capital', 'equity', 2, TRUE),
('311', 'رأس المال المدفوع', 'Paid Capital', 'equity', 3, FALSE),
('32', 'الأرباح المحتجزة', 'Retained Earnings', 'equity', 2, FALSE);

-- الإيرادات
INSERT INTO chart_of_accounts (account_code, account_name, account_name_en, account_type, account_level, is_main_account) VALUES
('4', 'الإيرادات', 'Revenue', 'revenue', 1, TRUE),
('41', 'إيرادات المبيعات', 'Sales Revenue', 'revenue', 2, TRUE),
('411', 'مبيعات الخبز', 'Bread Sales', 'revenue', 3, FALSE),
('412', 'مبيعات الحلويات', 'Sweets Sales', 'revenue', 3, FALSE),
('413', 'مبيعات المعجنات', 'Pastries Sales', 'revenue', 3, FALSE);

-- المصروفات
INSERT INTO chart_of_accounts (account_code, account_name, account_name_en, account_type, account_level, is_main_account) VALUES
('5', 'المصروفات', 'Expenses', 'expense', 1, TRUE),
('51', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 'expense', 2, TRUE),
('511', 'تكلفة المواد الخام', 'Raw Materials Cost', 'expense', 3, FALSE),
('52', 'المصروفات التشغيلية', 'Operating Expenses', 'expense', 2, TRUE),
('521', 'الرواتب والأجور', 'Salaries and Wages', 'expense', 3, FALSE),
('522', 'الإيجار', 'Rent', 'expense', 3, FALSE),
('523', 'الكهرباء والماء', 'Utilities', 'expense', 3, FALSE),
('524', 'مصاريف الصيانة', 'Maintenance', 'expense', 3, FALSE);

-- ===== الصناديق =====
INSERT INTO cash_boxes (name, description, account_id, opening_balance, current_balance) VALUES
('الصندوق الرئيسي', 'صندوق المبيعات الرئيسي', 4, 50000.000, 50000.000),
('صندوق المشتريات', 'صندوق خاص بالمشتريات', 4, 20000.000, 20000.000);

-- ===== البنوك =====
INSERT INTO banks (bank_name, account_number, iban, account_id, opening_balance, current_balance) VALUES
('البنك الأهلي اليمني', '*********0', 'YE12NBYI*********0123456', 5, 100000.000, 100000.000),
('بنك سبأ الإسلامي', '**********', 'YE34SABA**********098765', 5, 75000.000, 75000.000);

-- ===== الموظفين =====
INSERT INTO employees (employee_code, full_name, position, department, hire_date, salary, phone, national_id) VALUES
('EMP001', 'علي محمد الخباز', 'خباز رئيسي', 'الإنتاج', '2024-01-01', 80000.00, '*********', '0*********0'),
('EMP002', 'مريم أحمد الحلواني', 'حلواني', 'الإنتاج', '2024-01-15', 70000.00, '*********', '0*********1'),
('EMP003', 'خالد سالم البائع', 'بائع', 'المبيعات', '2024-02-01', 60000.00, '*********', '0*********2'),
('EMP004', 'نادية علي المحاسبة', 'محاسبة', 'الإدارة', '2024-01-10', 90000.00, '*********', '0*********3');

-- ===== فئات المنتجات =====
INSERT INTO product_categories (name, description) VALUES
('الخبز والعيش', 'جميع أنواع الخبز والعيش'),
('الحلويات الشرقية', 'البقلاوة والكنافة وغيرها'),
('الحلويات الغربية', 'الكيك والتورتات'),
('المعجنات المالحة', 'الفطائر والسمبوسة'),
('المشروبات', 'العصائر والمشروبات الساخنة'),
('المواد الخام', 'الدقيق والسكر وغيرها');

-- ===== المنتجات =====
INSERT INTO products (product_code, name, category_id, unit, cost_price, selling_price, stock_quantity, min_stock_level) VALUES
-- الخبز والعيش
('PRD001', 'خبز عربي كبير', 1, 'رغيف', 25.000, 50.000, 500.000, 50.000),
('PRD002', 'خبز عربي صغير', 1, 'رغيف', 15.000, 30.000, 300.000, 30.000),
('PRD003', 'خبز التوست', 1, 'كيس', 200.000, 350.000, 100.000, 10.000),
('PRD004', 'خبز البرجر', 1, 'قطعة', 50.000, 100.000, 80.000, 10.000),

-- الحلويات الشرقية
('PRD005', 'بقلاوة بالفستق', 2, 'كيلو', 2500.000, 4000.000, 20.000, 5.000),
('PRD006', 'كنافة بالجبن', 2, 'كيلو', 2000.000, 3500.000, 15.000, 3.000),
('PRD007', 'معمول بالتمر', 2, 'كيلو', 1800.000, 3000.000, 25.000, 5.000),

-- الحلويات الغربية
('PRD008', 'كيك الشوكولاتة', 3, 'قطعة', 800.000, 1500.000, 10.000, 2.000),
('PRD009', 'تورتة الفراولة', 3, 'قطعة', 1200.000, 2000.000, 8.000, 2.000),

-- المعجنات المالحة
('PRD010', 'فطيرة الجبن', 4, 'قطعة', 100.000, 200.000, 50.000, 10.000),
('PRD011', 'سمبوسة اللحم', 4, 'قطعة', 150.000, 250.000, 40.000, 10.000),

-- المشروبات
('PRD012', 'عصير برتقال طازج', 5, 'كوب', 100.000, 200.000, 30.000, 5.000),
('PRD013', 'شاي أحمر', 5, 'كوب', 25.000, 50.000, 100.000, 20.000),
('PRD014', 'قهوة عربية', 5, 'كوب', 50.000, 100.000, 80.000, 15.000);

-- ===== العملاء =====
INSERT INTO customers (customer_code, name, phone, address, credit_limit, current_balance) VALUES
('CUST001', 'مطعم الأصالة', '777555666', 'شارع الستين، صنعاء', 50000.000, 0.000),
('CUST002', 'كافيه المدينة', '777666777', 'شارع الزبيري، صنعاء', 30000.000, 5000.000),
('CUST003', 'مقهى الشباب', '777777888', 'شارع الثورة، صنعاء', 20000.000, 0.000),
('CUST004', 'فندق السلام', '777888999', 'شارع الحصبة، صنعاء', 100000.000, 15000.000),
('CUST005', 'عميل نقدي', '000000000', 'مبيعات نقدية', 0.000, 0.000);

-- ===== الموردين =====
INSERT INTO suppliers (supplier_code, name, phone, address, current_balance) VALUES
('SUPP001', 'مطاحن الحديدة للدقيق', '777111000', 'الحديدة، اليمن', 25000.000),
('SUPP002', 'شركة السكر اليمنية', '777222000', 'عدن، اليمن', 18000.000),
('SUPP003', 'مؤسسة الزيوت والسمن', '*********', 'صنعاء، اليمن', 12000.000),
('SUPP004', 'تجارة البيض والألبان', '*********', 'ذمار، اليمن', 8000.000);

-- ===== إعدادات النظام =====
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('invoice_prefix', 'INV', 'text', 'بادئة رقم الفاتورة'),
('receipt_prefix', 'REC', 'text', 'بادئة رقم الإيصال'),
('payment_prefix', 'PAY', 'text', 'بادئة رقم الدفع'),
('tax_rate', '0.00', 'decimal', 'معدل الضريبة %'),
('currency_decimal_places', '3', 'integer', 'عدد الخانات العشرية للعملة'),
('backup_frequency', 'daily', 'text', 'تكرار النسخ الاحتياطي'),
('low_stock_alert', '1', 'boolean', 'تنبيه نفاد المخزون'),
('auto_journal_entry', '1', 'boolean', 'القيود التلقائية');

-- ===== الأصول الثابتة =====
INSERT INTO fixed_assets (asset_code, asset_name, category, purchase_date, purchase_cost, useful_life_years, book_value) VALUES
('ASSET001', 'فرن الخبز الرئيسي', 'معدات الإنتاج', '2024-01-01', 500000.000, 10, 500000.000),
('ASSET002', 'ماكينة العجن الكبيرة', 'معدات الإنتاج', '2024-01-01', 200000.000, 8, 200000.000),
('ASSET003', 'ثلاجة العرض', 'معدات البيع', '2024-01-15', 150000.000, 5, 150000.000),
('ASSET004', 'سيارة التوصيل', 'وسائل النقل', '2024-02-01', 800000.000, 5, 800000.000);

-- تحديث الأرصدة الافتتاحية للحسابات
UPDATE chart_of_accounts SET opening_balance = 70000.000, current_balance = 70000.000 WHERE account_code = '1111';
UPDATE chart_of_accounts SET opening_balance = 175000.000, current_balance = 175000.000 WHERE account_code = '1112';
UPDATE chart_of_accounts SET opening_balance = 50000.000, current_balance = 50000.000 WHERE account_code = '1121';
UPDATE chart_of_accounts SET opening_balance = 80000.000, current_balance = 80000.000 WHERE account_code = '1122';
UPDATE chart_of_accounts SET opening_balance = 20000.000, current_balance = 20000.000 WHERE account_code = '1131';
UPDATE chart_of_accounts SET opening_balance = 43000.000, current_balance = 43000.000 WHERE account_code = '2111';
UPDATE chart_of_accounts SET opening_balance = 1000000.000, current_balance = 1000000.000 WHERE account_code = '311';

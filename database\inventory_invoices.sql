-- جداول المخزون والفواتير والسندات
-- Inventory, Invoices and Vouchers Tables

-- ===================================
-- جدول وحدات القياس
-- ===================================
CREATE TABLE units (
    id INT PRIMARY KEY AUTO_INCREMENT,
    unit_name VARCHAR(50) NOT NULL COMMENT 'اسم الوحدة',
    unit_symbol VARCHAR(10) NOT NULL COMMENT 'رمز الوحدة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول فئات الأصناف
-- ===================================
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL COMMENT 'اسم الفئة',
    category_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز الفئة',
    parent_id INT NULL COMMENT 'الفئة الأب',
    description TEXT COMMENT 'وصف الفئة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول الأصناف (خامات ومنتجات وخدمات)
-- ===================================
CREATE TABLE items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز الصنف',
    item_name VARCHAR(255) NOT NULL COMMENT 'اسم الصنف',
    barcode VARCHAR(50) COMMENT 'الباركود',
    category_id INT COMMENT 'معرف الفئة',
    item_type ENUM('raw_material', 'product', 'service') NOT NULL COMMENT 'نوع الصنف',
    main_unit_id INT NOT NULL COMMENT 'الوحدة الرئيسية',
    sub_unit_id INT COMMENT 'الوحدة الفرعية',
    conversion_factor DECIMAL(10,4) DEFAULT 1.0000 COMMENT 'معامل التحويل',
    purchase_price DECIMAL(10,2) DEFAULT 0.00 COMMENT 'سعر الشراء',
    selling_price DECIMAL(10,2) DEFAULT 0.00 COMMENT 'سعر البيع',
    profit_margin DECIMAL(5,2) DEFAULT 0.00 COMMENT 'هامش الربح %',
    min_stock DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الحد الأدنى للمخزون',
    max_stock DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الحد الأقصى للمخزون',
    current_stock DECIMAL(10,2) DEFAULT 0.00 COMMENT 'المخزون الحالي',
    reorder_level DECIMAL(10,2) DEFAULT 0.00 COMMENT 'نقطة إعادة الطلب',
    account_id INT COMMENT 'معرف الحساب المرتبط',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    description TEXT COMMENT 'وصف الصنف',
    image VARCHAR(255) COMMENT 'صورة الصنف',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (main_unit_id) REFERENCES units(id),
    FOREIGN KEY (sub_unit_id) REFERENCES units(id),
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول وصفات المنتجات
-- ===================================
CREATE TABLE product_recipes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL COMMENT 'معرف المنتج',
    raw_material_id INT NOT NULL COMMENT 'معرف الخامة',
    quantity DECIMAL(10,4) NOT NULL COMMENT 'الكمية المطلوبة',
    unit_id INT NOT NULL COMMENT 'وحدة القياس',
    cost_per_unit DECIMAL(10,2) DEFAULT 0.00 COMMENT 'التكلفة لكل وحدة',
    total_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT 'إجمالي التكلفة',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES items(id),
    FOREIGN KEY (raw_material_id) REFERENCES items(id),
    FOREIGN KEY (unit_id) REFERENCES units(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول العملاء
-- ===================================
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز العميل',
    customer_name VARCHAR(255) NOT NULL COMMENT 'اسم العميل',
    customer_type ENUM('individual', 'company') DEFAULT 'individual' COMMENT 'نوع العميل',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    mobile VARCHAR(20) COMMENT 'رقم الجوال',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    address TEXT COMMENT 'العنوان',
    tax_number VARCHAR(50) COMMENT 'الرقم الضريبي',
    credit_limit DECIMAL(15,2) DEFAULT 0.00 COMMENT 'حد الائتمان',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الحالي',
    account_id INT COMMENT 'معرف الحساب المرتبط',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول الموردين
-- ===================================
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز المورد',
    supplier_name VARCHAR(255) NOT NULL COMMENT 'اسم المورد',
    supplier_type ENUM('individual', 'company') DEFAULT 'company' COMMENT 'نوع المورد',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    mobile VARCHAR(20) COMMENT 'رقم الجوال',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    address TEXT COMMENT 'العنوان',
    tax_number VARCHAR(50) COMMENT 'الرقم الضريبي',
    credit_limit DECIMAL(15,2) DEFAULT 0.00 COMMENT 'حد الائتمان',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الحالي',
    account_id INT COMMENT 'معرف الحساب المرتبط',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول الفواتير
-- ===================================
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم الفاتورة',
    invoice_type ENUM('sales', 'purchase', 'sales_return', 'purchase_return', 'quick_sale') NOT NULL COMMENT 'نوع الفاتورة',
    invoice_date DATE NOT NULL COMMENT 'تاريخ الفاتورة',
    customer_id INT COMMENT 'معرف العميل',
    supplier_id INT COMMENT 'معرف المورد',
    cash_box_id INT COMMENT 'معرف الصندوق',
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المجموع الفرعي',
    discount_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'مبلغ الخصم',
    discount_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الخصم',
    tax_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'مبلغ الضريبة',
    tax_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'المبلغ الإجمالي',
    paid_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'المبلغ المدفوع',
    remaining_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'المبلغ المتبقي',
    payment_status ENUM('pending', 'partial', 'paid') DEFAULT 'pending' COMMENT 'حالة الدفع',
    journal_entry_id INT COMMENT 'معرف القيد المحاسبي',
    notes TEXT COMMENT 'ملاحظات',
    is_posted BOOLEAN DEFAULT FALSE COMMENT 'مرحل',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول تفاصيل الفواتير
-- ===================================
CREATE TABLE invoice_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL COMMENT 'معرف الفاتورة',
    item_id INT NOT NULL COMMENT 'معرف الصنف',
    quantity DECIMAL(10,4) NOT NULL COMMENT 'الكمية',
    unit_id INT NOT NULL COMMENT 'وحدة القياس',
    unit_price DECIMAL(10,2) NOT NULL COMMENT 'سعر الوحدة',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT 'مبلغ الخصم',
    discount_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الخصم',
    total_amount DECIMAL(15,2) NOT NULL COMMENT 'المبلغ الإجمالي',
    notes TEXT COMMENT 'ملاحظات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id),
    FOREIGN KEY (unit_id) REFERENCES units(id)
);

-- ===================================
-- جدول السندات (قبض وصرف)
-- ===================================
CREATE TABLE vouchers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    voucher_number VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم السند',
    voucher_type ENUM('receipt', 'payment') NOT NULL COMMENT 'نوع السند',
    voucher_date DATE NOT NULL COMMENT 'تاريخ السند',
    party_type ENUM('customer', 'supplier', 'employee', 'owner', 'expense', 'revenue', 'other') NOT NULL COMMENT 'نوع الطرف',
    party_id INT COMMENT 'معرف الطرف',
    party_name VARCHAR(255) COMMENT 'اسم الطرف',
    cash_box_id INT COMMENT 'معرف الصندوق',
    bank_id INT COMMENT 'معرف البنك',
    amount DECIMAL(15,2) NOT NULL COMMENT 'المبلغ',
    description TEXT NOT NULL COMMENT 'البيان',
    reference_type ENUM('invoice', 'salary', 'advance', 'loan', 'other') COMMENT 'نوع المرجع',
    reference_id INT COMMENT 'معرف المرجع',
    journal_entry_id INT COMMENT 'معرف القيد المحاسبي',
    is_posted BOOLEAN DEFAULT FALSE COMMENT 'مرحل',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
    FOREIGN KEY (bank_id) REFERENCES banks(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول تفاصيل السندات
-- ===================================
CREATE TABLE voucher_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    voucher_id INT NOT NULL COMMENT 'معرف السند',
    account_id INT NOT NULL COMMENT 'معرف الحساب',
    amount DECIMAL(15,2) NOT NULL COMMENT 'المبلغ',
    description TEXT COMMENT 'البيان',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- ===================================
-- جدول حركة المخزون
-- ===================================
CREATE TABLE stock_movements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL COMMENT 'معرف الصنف',
    movement_type ENUM('in', 'out', 'transfer', 'adjustment', 'production') NOT NULL COMMENT 'نوع الحركة',
    reference_type ENUM('invoice', 'voucher', 'production', 'adjustment', 'opening') NOT NULL COMMENT 'نوع المرجع',
    reference_id INT COMMENT 'معرف المرجع',
    quantity DECIMAL(10,4) NOT NULL COMMENT 'الكمية',
    unit_id INT NOT NULL COMMENT 'وحدة القياس',
    unit_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT 'تكلفة الوحدة',
    total_cost DECIMAL(15,2) DEFAULT 0.00 COMMENT 'إجمالي التكلفة',
    balance_before DECIMAL(10,4) NOT NULL COMMENT 'الرصيد قبل الحركة',
    balance_after DECIMAL(10,4) NOT NULL COMMENT 'الرصيد بعد الحركة',
    movement_date DATE NOT NULL COMMENT 'تاريخ الحركة',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id),
    FOREIGN KEY (unit_id) REFERENCES units(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول الأصول الثابتة
-- ===================================
CREATE TABLE fixed_assets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    asset_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز الأصل',
    asset_name VARCHAR(255) NOT NULL COMMENT 'اسم الأصل',
    asset_category VARCHAR(100) COMMENT 'فئة الأصل',
    purchase_date DATE NOT NULL COMMENT 'تاريخ الشراء',
    purchase_cost DECIMAL(15,2) NOT NULL COMMENT 'تكلفة الشراء',
    useful_life_years INT NOT NULL COMMENT 'العمر الإنتاجي بالسنوات',
    depreciation_method ENUM('straight_line', 'declining_balance', 'units_of_production') DEFAULT 'straight_line' COMMENT 'طريقة الإهلاك',
    depreciation_rate DECIMAL(5,2) COMMENT 'معدل الإهلاك',
    accumulated_depreciation DECIMAL(15,2) DEFAULT 0.00 COMMENT 'مجمع الإهلاك',
    book_value DECIMAL(15,2) COMMENT 'القيمة الدفترية',
    account_id INT COMMENT 'معرف حساب الأصل',
    depreciation_account_id INT COMMENT 'معرف حساب الإهلاك',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (depreciation_account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول إهلاك الأصول
-- ===================================
CREATE TABLE asset_depreciation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    asset_id INT NOT NULL COMMENT 'معرف الأصل',
    depreciation_date DATE NOT NULL COMMENT 'تاريخ الإهلاك',
    depreciation_amount DECIMAL(15,2) NOT NULL COMMENT 'مبلغ الإهلاك',
    accumulated_depreciation DECIMAL(15,2) NOT NULL COMMENT 'مجمع الإهلاك',
    book_value DECIMAL(15,2) NOT NULL COMMENT 'القيمة الدفترية',
    journal_entry_id INT COMMENT 'معرف القيد المحاسبي',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES fixed_assets(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

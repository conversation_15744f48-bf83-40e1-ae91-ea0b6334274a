<?php
/**
 * وحدة إدارة المستخدمين
 * Users Management Module
 */

// تضمين الملفات المطلوبة
require_once '../../config/database.php';
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/validation.php';

// التحقق من الصلاحيات
requirePermission('users', 'view');

$db = getDB();
$error = '';
$success = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add':
            if (!hasPermission('users', 'add')) {
                $error = 'ليس لديك صلاحية لإضافة مستخدم جديد';
                break;
            }

            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('username', 'اسم المستخدم مطلوب')
                     ->required('password', 'كلمة المرور مطلوبة')
                     ->required('full_name', 'الاسم الكامل مطلوب')
                     ->required('role', 'الدور مطلوب')
                     ->minLength('username', 3, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
                     ->minLength('password', 6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
                     ->unique('username', 'users', 'username', null, 'اسم المستخدم موجود مسبقاً')
                     ->email('email', 'البريد الإلكتروني غير صحيح')
                     ->in('role', array_keys(USER_ROLES), 'الدور المحدد غير صحيح');

            if (!$validator->hasErrors()) {
                try {
                    $permissions = [];
                    if (isset($_POST['permissions']) && is_array($_POST['permissions'])) {
                        foreach ($_POST['permissions'] as $module => $actions) {
                            if (is_array($actions)) {
                                $permissions[$module] = $actions;
                            }
                        }
                    }

                    $user_id = $db->insert(
                        "INSERT INTO users (username, password, full_name, email, phone, role, permissions, is_active, created_by)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        [
                            cleanInput($_POST['username']),
                            password_hash($_POST['password'], PASSWORD_DEFAULT),
                            cleanInput($_POST['full_name']),
                            cleanInput($_POST['email']),
                            cleanInput($_POST['phone']),
                            $_POST['role'],
                            json_encode($permissions),
                            isset($_POST['is_active']) ? 1 : 0,
                            $_SESSION['user_id']
                        ]
                    );

                    logUserActivity('create', 'users', $user_id, null, $_POST);
                    $success = 'تم إضافة المستخدم بنجاح';

                } catch (Exception $e) {
                    $error = 'خطأ في إضافة المستخدم: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;

        case 'edit':
            if (!hasPermission('users', 'edit')) {
                $error = 'ليس لديك صلاحية لتعديل المستخدم';
                break;
            }

            $user_id = intval($_POST['user_id']);

            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('full_name', 'الاسم الكامل مطلوب')
                     ->required('role', 'الدور مطلوب')
                     ->unique('username', 'users', 'username', $user_id, 'اسم المستخدم موجود مسبقاً')
                     ->email('email', 'البريد الإلكتروني غير صحيح')
                     ->in('role', array_keys(USER_ROLES), 'الدور المحدد غير صحيح');

            if (!empty($_POST['password'])) {
                $validator->minLength('password', 6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }

            if (!$validator->hasErrors()) {
                try {
                    $old_data = $db->selectOne("SELECT * FROM users WHERE id = ?", [$user_id]);

                    $permissions = [];
                    if (isset($_POST['permissions']) && is_array($_POST['permissions'])) {
                        foreach ($_POST['permissions'] as $module => $actions) {
                            if (is_array($actions)) {
                                $permissions[$module] = $actions;
                            }
                        }
                    }

                    $update_data = [
                        cleanInput($_POST['username']),
                        cleanInput($_POST['full_name']),
                        cleanInput($_POST['email']),
                        cleanInput($_POST['phone']),
                        $_POST['role'],
                        json_encode($permissions),
                        isset($_POST['is_active']) ? 1 : 0,
                        $user_id
                    ];

                    $query = "UPDATE users SET username = ?, full_name = ?, email = ?, phone = ?, role = ?, permissions = ?, is_active = ?, updated_at = NOW()";

                    if (!empty($_POST['password'])) {
                        $query .= ", password = ?";
                        array_splice($update_data, -1, 0, [password_hash($_POST['password'], PASSWORD_DEFAULT)]);
                    }

                    $query .= " WHERE id = ?";

                    $db->update($query, $update_data);

                    logUserActivity('update', 'users', $user_id, $old_data, $_POST);
                    $success = 'تم تحديث المستخدم بنجاح';

                } catch (Exception $e) {
                    $error = 'خطأ في تحديث المستخدم: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;

        case 'toggle_status':
            if (!hasPermission('users', 'edit')) {
                $error = 'ليس لديك صلاحية لتغيير حالة المستخدم';
                break;
            }

            $user_id = intval($_POST['user_id']);

            if ($user_id == $_SESSION['user_id']) {
                $error = 'لا يمكنك تعطيل حسابك الخاص';
                break;
            }

            try {
                $user = $db->selectOne("SELECT is_active FROM users WHERE id = ?", [$user_id]);
                $new_status = $user['is_active'] ? 0 : 1;

                $db->update("UPDATE users SET is_active = ? WHERE id = ?", [$new_status, $user_id]);

                logUserActivity($new_status ? 'activate' : 'deactivate', 'users', $user_id);
                $success = $new_status ? 'تم تفعيل المستخدم' : 'تم تعطيل المستخدم';

            } catch (Exception $e) {
                $error = 'خطأ في تغيير حالة المستخدم: ' . $e->getMessage();
            }
            break;
    }
}

// جلب قائمة المستخدمين
try {
    $users = $db->select(
        "SELECT u.*, creator.full_name as created_by_name
         FROM users u
         LEFT JOIN users creator ON u.created_by = creator.id
         ORDER BY u.created_at DESC"
    );
} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
}

$pageTitle = 'إدارة المستخدمين';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="../../assets/css/style.css">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .role-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .role-admin { background-color: #dc3545; color: white; }
        .role-manager { background-color: #fd7e14; color: white; }
        .role-accountant { background-color: #20c997; color: white; }
        .role-cashier { background-color: #6f42c1; color: white; }
        .role-user { background-color: #6c757d; color: white; }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .permission-module {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .permission-module h6 {
            margin-bottom: 0.5rem;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 0.5rem;
        }

        .permission-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .permission-check {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.9rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 2% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الهيدر -->
        <header class="header">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-users"></i> <?php echo $pageTitle; ?></h1>
                <div>
                    <?php if (hasPermission('users', 'add')): ?>
                    <button type="button" class="btn btn-primary" onclick="openAddModal()">
                        <i class="fas fa-plus"></i> إضافة مستخدم جديد
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </header>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <!-- جدول المستخدمين -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> قائمة المستخدمين</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($users)): ?>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar">
                                                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                            </div>
                                            <div class="ms-3">
                                                <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                <?php if ($user['phone']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email'] ?? '-'); ?></td>
                                    <td>
                                        <span class="role-badge role-<?php echo $user['role']; ?>">
                                            <?php echo USER_ROLES[$user['role']] ?? $user['role']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge <?php echo $user['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $user['is_active'] ? 'نشط' : 'معطل'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($user['last_login']): ?>
                                            <?php echo formatDateTime($user['last_login']); ?>
                                        <?php else: ?>
                                            <span class="text-muted">لم يسجل دخول</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (hasPermission('users', 'edit')): ?>
                                        <button type="button" class="btn btn-sm btn-warning"
                                                onclick="openEditModal(<?php echo htmlspecialchars(json_encode($user)); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" class="btn btn-sm <?php echo $user['is_active'] ? 'btn-danger' : 'btn-success'; ?>"
                                                    onclick="return confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')">
                                                <i class="fas fa-<?php echo $user['is_active'] ? 'ban' : 'check'; ?>"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        <?php endif; ?>

                                        <button type="button" class="btn btn-sm btn-info"
                                                onclick="viewUserLogs(<?php echo $user['id']; ?>)">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center">لا توجد مستخدمين مسجلين</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة مستخدم جديد -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addModal')">&times;</span>
            <h3><i class="fas fa-plus"></i> إضافة مستخدم جديد</h3>

            <form method="POST" action="">
                <input type="hidden" name="action" value="add">

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="username" class="form-label">اسم المستخدم *</label>
                            <input type="text" name="username" id="username" class="form-control" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password" class="form-label">كلمة المرور *</label>
                            <input type="password" name="password" id="password" class="form-control" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                    <input type="text" name="full_name" id="full_name" class="form-control" required>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="email" id="email" class="form-control">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" id="phone" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="role" class="form-label">الدور *</label>
                            <select name="role" id="role" class="form-select" required>
                                <option value="">اختر الدور</option>
                                <?php foreach (USER_ROLES as $role => $name): ?>
                                <option value="<?php echo $role; ?>"><?php echo $name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" id="is_active" class="form-check-input" checked>
                                <label for="is_active" class="form-check-label">مستخدم نشط</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصلاحيات -->
                <div class="form-group">
                    <label class="form-label">الصلاحيات</label>
                    <div class="permissions-grid">
                        <?php foreach (SYSTEM_MODULES as $module => $name): ?>
                        <div class="permission-module">
                            <h6><?php echo $name; ?></h6>
                            <div class="permission-actions">
                                <?php foreach (PERMISSION_LEVELS as $action => $action_name): ?>
                                <div class="permission-check">
                                    <input type="checkbox"
                                           name="permissions[<?php echo $module; ?>][<?php echo $action; ?>]"
                                           id="perm_<?php echo $module; ?>_<?php echo $action; ?>"
                                           class="form-check-input">
                                    <label for="perm_<?php echo $module; ?>_<?php echo $action; ?>" class="form-check-label">
                                        <?php echo $action_name; ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="form-group text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ المستخدم
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل المستخدم -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('editModal')">&times;</span>
            <h3><i class="fas fa-edit"></i> تعديل المستخدم</h3>

            <form method="POST" action="">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="user_id" id="edit_user_id">

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_username" class="form-label">اسم المستخدم *</label>
                            <input type="text" name="username" id="edit_username" class="form-control" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" name="password" id="edit_password" class="form-control">
                            <small class="text-muted">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit_full_name" class="form-label">الاسم الكامل *</label>
                    <input type="text" name="full_name" id="edit_full_name" class="form-control" required>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="email" id="edit_email" class="form-control">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_phone" class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" id="edit_phone" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_role" class="form-label">الدور *</label>
                            <select name="role" id="edit_role" class="form-select" required>
                                <?php foreach (USER_ROLES as $role => $name): ?>
                                <option value="<?php echo $role; ?>"><?php echo $name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" id="edit_is_active" class="form-check-input">
                                <label for="edit_is_active" class="form-check-label">مستخدم نشط</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصلاحيات -->
                <div class="form-group">
                    <label class="form-label">الصلاحيات</label>
                    <div class="permissions-grid" id="edit_permissions_grid">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>

                <div class="form-group text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }

        function openEditModal(user) {
            document.getElementById('edit_user_id').value = user.id;
            document.getElementById('edit_username').value = user.username;
            document.getElementById('edit_full_name').value = user.full_name;
            document.getElementById('edit_email').value = user.email || '';
            document.getElementById('edit_phone').value = user.phone || '';
            document.getElementById('edit_role').value = user.role;
            document.getElementById('edit_is_active').checked = user.is_active == 1;

            // تحميل الصلاحيات
            loadUserPermissions(user.permissions);

            document.getElementById('editModal').style.display = 'block';
        }

        function loadUserPermissions(permissionsJson) {
            const permissions = permissionsJson ? JSON.parse(permissionsJson) : {};
            const grid = document.getElementById('edit_permissions_grid');

            let html = '';
            const modules = <?php echo json_encode(SYSTEM_MODULES); ?>;
            const levels = <?php echo json_encode(PERMISSION_LEVELS); ?>;

            for (const [module, name] of Object.entries(modules)) {
                html += `<div class="permission-module">
                    <h6>${name}</h6>
                    <div class="permission-actions">`;

                for (const [action, actionName] of Object.entries(levels)) {
                    const checked = permissions[module] && permissions[module][action] ? 'checked' : '';
                    html += `<div class="permission-check">
                        <input type="checkbox"
                               name="permissions[${module}][${action}]"
                               id="edit_perm_${module}_${action}"
                               class="form-check-input" ${checked}>
                        <label for="edit_perm_${module}_${action}" class="form-check-label">
                            ${actionName}
                        </label>
                    </div>`;
                }

                html += `</div></div>`;
            }

            grid.innerHTML = html;
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function viewUserLogs(userId) {
            window.open(`user_logs.php?user_id=${userId}`, '_blank');
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // تحديد/إلغاء تحديد جميع الصلاحيات لوحدة معينة
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('module-select-all')) {
                const module = e.target.dataset.module;
                const checkboxes = document.querySelectorAll(`input[name^="permissions[${module}]"]`);
                checkboxes.forEach(cb => cb.checked = e.target.checked);
            }
        });
    </script>
</body>
</html>

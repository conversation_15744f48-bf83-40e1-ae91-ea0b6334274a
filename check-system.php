<?php
/**
 * فحص سريع لحالة النظام والمتطلبات
 * Quick System Check for Requirements
 */

// إعدادات الفحص
$checks = [];
$errors = [];
$warnings = [];

// فحص PHP
$phpVersion = phpversion();
$checks['PHP Version'] = $phpVersion;
if (version_compare($phpVersion, '7.4.0', '>=')) {
    $checks['PHP Status'] = '✅ متوافق';
} else {
    $errors[] = 'PHP 7.4+ مطلوب، الإصدار الحالي: ' . $phpVersion;
    $checks['PHP Status'] = '❌ غير متوافق';
}

// فحص امتدادات PHP المطلوبة
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl'];
$checks['PHP Extensions'] = [];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        $checks['PHP Extensions'][$ext] = '✅ متوفر';
    } else {
        $errors[] = "امتداد PHP مطلوب: $ext";
        $checks['PHP Extensions'][$ext] = '❌ غير متوفر';
    }
}

// فحص قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
    $checks['MySQL Connection'] = '✅ متصل';
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'bakery_system'");
    if ($stmt->rowCount() > 0) {
        $checks['Database bakery_system'] = '✅ موجودة';
        
        // فحص الجداول
        $pdo->exec("USE bakery_system");
        $tables = ['users', 'company_settings', 'chart_of_accounts', 'cash_boxes', 'banks', 'employees'];
        $checks['Database Tables'] = [];
        
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $checks['Database Tables'][$table] = '✅ موجود';
            } else {
                $warnings[] = "جدول مفقود: $table";
                $checks['Database Tables'][$table] = '⚠️ مفقود';
            }
        }
    } else {
        $warnings[] = 'قاعدة البيانات bakery_system غير موجودة';
        $checks['Database bakery_system'] = '⚠️ غير موجودة';
    }
    
} catch (PDOException $e) {
    $errors[] = 'فشل الاتصال بـ MySQL: ' . $e->getMessage();
    $checks['MySQL Connection'] = '❌ فشل الاتصال';
}

// فحص الملفات المطلوبة
$requiredFiles = [
    'laragon-app.php' => 'الملف الرئيسي',
    'setup-database.php' => 'معالج الإعداد',
    'offline-app.html' => 'النسخة المحلية'
];

$checks['Required Files'] = [];
foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        $checks['Required Files'][$file] = '✅ موجود';
    } else {
        $warnings[] = "ملف مفقود: $file ($description)";
        $checks['Required Files'][$file] = '⚠️ مفقود';
    }
}

// فحص صلاحيات الكتابة
$writableDirs = ['./', '../'];
$checks['Write Permissions'] = [];

foreach ($writableDirs as $dir) {
    if (is_writable($dir)) {
        $checks['Write Permissions'][$dir] = '✅ قابل للكتابة';
    } else {
        $warnings[] = "مجلد غير قابل للكتابة: $dir";
        $checks['Write Permissions'][$dir] = '⚠️ غير قابل للكتابة';
    }
}

// فحص متغيرات الخادم
$checks['Server Info'] = [
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد',
    'HTTP Host' => $_SERVER['HTTP_HOST'] ?? 'غير محدد',
    'Request URI' => $_SERVER['REQUEST_URI'] ?? 'غير محدد'
];

// تحديد حالة النظام العامة
$systemStatus = 'جاهز';
if (!empty($errors)) {
    $systemStatus = 'يحتاج إصلاح';
} elseif (!empty($warnings)) {
    $systemStatus = 'يحتاج إعداد';
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام - نظام إدارة المخبز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: bold;
            margin-top: 1rem;
        }

        .status-ready { background: #28a745; }
        .status-setup { background: #ffc107; color: #212529; }
        .status-error { background: #dc3545; }

        .content {
            padding: 2rem;
        }

        .section {
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }

        .section-body {
            padding: 1rem;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-right-color: #dc3545;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-right-color: #ffc107;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-right-color: #28a745;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .container { margin: 1rem; }
            .content { padding: 1rem; }
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-stethoscope"></i> فحص النظام</h1>
            <p>فحص شامل لمتطلبات وحالة نظام إدارة المخبز</p>
            <div class="status-badge status-<?php echo $systemStatus === 'جاهز' ? 'ready' : ($systemStatus === 'يحتاج إعداد' ? 'setup' : 'error'); ?>">
                <?php echo $systemStatus; ?>
            </div>
        </div>
        
        <div class="content">
            <!-- الأخطاء -->
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-circle"></i> أخطاء يجب إصلاحها:</h4>
                <ul>
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- التحذيرات -->
            <?php if (!empty($warnings)): ?>
            <div class="alert alert-warning">
                <h4><i class="fas fa-exclamation-triangle"></i> تحذيرات:</h4>
                <ul>
                    <?php foreach ($warnings as $warning): ?>
                    <li><?php echo htmlspecialchars($warning); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- النجاح -->
            <?php if (empty($errors) && empty($warnings)): ?>
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle"></i> النظام جاهز للعمل!</h4>
                <p>جميع المتطلبات متوفرة والنظام يعمل بشكل صحيح.</p>
                <a href="laragon-app.php" class="btn">
                    <i class="fas fa-rocket"></i> تشغيل النظام
                </a>
            </div>
            <?php endif; ?>

            <!-- تفاصيل الفحص -->
            <div class="grid">
                <!-- فحص PHP -->
                <div class="section">
                    <div class="section-header">
                        <i class="fas fa-code"></i> PHP
                    </div>
                    <div class="section-body">
                        <div class="check-item">
                            <span>الإصدار</span>
                            <span><?php echo $checks['PHP Version']; ?></span>
                        </div>
                        <div class="check-item">
                            <span>الحالة</span>
                            <span><?php echo $checks['PHP Status']; ?></span>
                        </div>
                        <?php foreach ($checks['PHP Extensions'] as $ext => $status): ?>
                        <div class="check-item">
                            <span><?php echo $ext; ?></span>
                            <span><?php echo $status; ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- فحص قاعدة البيانات -->
                <div class="section">
                    <div class="section-header">
                        <i class="fas fa-database"></i> قاعدة البيانات
                    </div>
                    <div class="section-body">
                        <div class="check-item">
                            <span>اتصال MySQL</span>
                            <span><?php echo $checks['MySQL Connection']; ?></span>
                        </div>
                        <div class="check-item">
                            <span>قاعدة البيانات</span>
                            <span><?php echo $checks['Database bakery_system']; ?></span>
                        </div>
                        <?php if (isset($checks['Database Tables'])): ?>
                        <?php foreach ($checks['Database Tables'] as $table => $status): ?>
                        <div class="check-item">
                            <span><?php echo $table; ?></span>
                            <span><?php echo $status; ?></span>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- فحص الملفات -->
                <div class="section">
                    <div class="section-header">
                        <i class="fas fa-file-code"></i> الملفات المطلوبة
                    </div>
                    <div class="section-body">
                        <?php foreach ($checks['Required Files'] as $file => $status): ?>
                        <div class="check-item">
                            <span><?php echo $file; ?></span>
                            <span><?php echo $status; ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- معلومات الخادم -->
                <div class="section">
                    <div class="section-header">
                        <i class="fas fa-server"></i> معلومات الخادم
                    </div>
                    <div class="section-body">
                        <?php foreach ($checks['Server Info'] as $key => $value): ?>
                        <div class="check-item">
                            <span><?php echo $key; ?></span>
                            <span style="font-size: 0.9em; color: #666;"><?php echo htmlspecialchars($value); ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- أزرار العمل -->
            <div style="text-align: center; margin-top: 2rem;">
                <?php if ($systemStatus === 'يحتاج إعداد'): ?>
                <a href="setup-database.php" class="btn">
                    <i class="fas fa-cog"></i> إعداد قاعدة البيانات
                </a>
                <?php endif; ?>
                
                <a href="laragon-app.php" class="btn">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                
                <a href="offline-app.html" class="btn">
                    <i class="fas fa-laptop"></i> النسخة المحلية
                </a>
            </div>
        </div>
    </div>

    <!-- زر التحديث -->
    <button class="refresh-btn" onclick="location.reload()" title="تحديث الفحص">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(() => {
            location.reload();
        }, 30000);

        console.log('🔍 فحص النظام مكتمل');
        console.log('📊 الحالة:', '<?php echo $systemStatus; ?>');
    </script>
</body>
</html>

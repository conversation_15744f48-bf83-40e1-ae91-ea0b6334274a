<?php
/**
 * وحدة شجرة الحسابات المحاسبية
 * Chart of Accounts Module
 */

// تضمين الملفات المطلوبة
require_once '../../config/database.php';
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/validation.php';

// التحقق من الصلاحيات
requirePermission('accounts', 'view');

$db = getDB();
$error = '';
$success = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            if (!hasPermission('accounts', 'add')) {
                $error = 'ليس لديك صلاحية لإضافة حساب جديد';
                break;
            }
            
            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('account_code', 'رمز الحساب مطلوب')
                     ->required('account_name', 'اسم الحساب مطلوب')
                     ->required('account_type', 'نوع الحساب مطلوب')
                     ->unique('account_code', 'chart_of_accounts', 'account_code', null, 'رمز الحساب موجود مسبقاً')
                     ->in('account_type', ['asset', 'liability', 'equity', 'revenue', 'expense'], 'نوع الحساب غير صحيح');
            
            if (!$validator->hasErrors()) {
                try {
                    $db->beginTransaction();
                    
                    // تحديد مستوى الحساب
                    $parent_id = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
                    $account_level = 1;
                    
                    if ($parent_id) {
                        $parent = $db->selectOne("SELECT account_level FROM chart_of_accounts WHERE id = ?", [$parent_id]);
                        if ($parent) {
                            $account_level = $parent['account_level'] + 1;
                        }
                    }
                    
                    // إدراج الحساب الجديد
                    $account_id = $db->insert(
                        "INSERT INTO chart_of_accounts (account_code, account_name, account_name_en, parent_id, account_type, account_level, opening_balance, description, created_by) 
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        [
                            cleanInput($_POST['account_code']),
                            cleanInput($_POST['account_name']),
                            cleanInput($_POST['account_name_en']),
                            $parent_id,
                            $_POST['account_type'],
                            $account_level,
                            floatval($_POST['opening_balance'] ?? 0),
                            cleanInput($_POST['description']),
                            $_SESSION['user_id']
                        ]
                    );
                    
                    // إنشاء قيد افتتاحي إذا كان هناك رصيد
                    $opening_balance = floatval($_POST['opening_balance'] ?? 0);
                    if ($opening_balance != 0) {
                        $entries = [];
                        
                        if (in_array($_POST['account_type'], ['asset', 'expense'])) {
                            // الأصول والمصروفات مدينة
                            $entries[] = [
                                'account_id' => $account_id,
                                'debit_amount' => $opening_balance,
                                'credit_amount' => 0,
                                'description' => 'رصيد افتتاحي'
                            ];
                        } else {
                            // الخصوم وحقوق الملكية والإيرادات دائنة
                            $entries[] = [
                                'account_id' => $account_id,
                                'debit_amount' => 0,
                                'credit_amount' => $opening_balance,
                                'description' => 'رصيد افتتاحي'
                            ];
                        }
                        
                        // البحث عن حساب رأس المال أو إنشاء قيد مؤقت
                        $capital_account = $db->selectOne("SELECT id FROM chart_of_accounts WHERE account_type = 'equity' ORDER BY id LIMIT 1");
                        if ($capital_account) {
                            if (in_array($_POST['account_type'], ['asset', 'expense'])) {
                                $entries[] = [
                                    'account_id' => $capital_account['id'],
                                    'debit_amount' => 0,
                                    'credit_amount' => $opening_balance,
                                    'description' => 'مقابل رصيد افتتاحي'
                                ];
                            } else {
                                $entries[] = [
                                    'account_id' => $capital_account['id'],
                                    'debit_amount' => $opening_balance,
                                    'credit_amount' => 0,
                                    'description' => 'مقابل رصيد افتتاحي'
                                ];
                            }
                            
                            createJournalEntry('قيد افتتاحي - ' . $_POST['account_name'], $entries, 'opening', $account_id);
                        }
                    }
                    
                    $db->commit();
                    
                    logUserActivity('create', 'chart_of_accounts', $account_id, null, $_POST);
                    $success = 'تم إضافة الحساب بنجاح';
                    
                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'خطأ في إضافة الحساب: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;
            
        case 'edit':
            if (!hasPermission('accounts', 'edit')) {
                $error = 'ليس لديك صلاحية لتعديل الحساب';
                break;
            }
            
            $account_id = intval($_POST['account_id']);
            
            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('account_name', 'اسم الحساب مطلوب')
                     ->unique('account_code', 'chart_of_accounts', 'account_code', $account_id, 'رمز الحساب موجود مسبقاً');
            
            if (!$validator->hasErrors()) {
                try {
                    $old_data = $db->selectOne("SELECT * FROM chart_of_accounts WHERE id = ?", [$account_id]);
                    
                    $db->update(
                        "UPDATE chart_of_accounts SET account_name = ?, account_name_en = ?, description = ?, updated_at = NOW() WHERE id = ?",
                        [
                            cleanInput($_POST['account_name']),
                            cleanInput($_POST['account_name_en']),
                            cleanInput($_POST['description']),
                            $account_id
                        ]
                    );
                    
                    logUserActivity('update', 'chart_of_accounts', $account_id, $old_data, $_POST);
                    $success = 'تم تحديث الحساب بنجاح';
                    
                } catch (Exception $e) {
                    $error = 'خطأ في تحديث الحساب: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;
    }
}

// جلب شجرة الحسابات
try {
    $accounts = $db->select(
        "SELECT a.*, 
                COALESCE(p.account_name, 'حساب رئيسي') as parent_name,
                (SELECT COUNT(*) FROM chart_of_accounts WHERE parent_id = a.id) as children_count
         FROM chart_of_accounts a
         LEFT JOIN chart_of_accounts p ON a.parent_id = p.id
         ORDER BY a.account_code"
    );
    
    // جلب الحسابات الرئيسية للقائمة المنسدلة
    $parent_accounts = $db->select(
        "SELECT id, account_code, account_name, account_level 
         FROM chart_of_accounts 
         WHERE account_level <= 3
         ORDER BY account_code"
    );
    
} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
}

$pageTitle = 'شجرة الحسابات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .account-tree {
            font-family: monospace;
        }
        
        .account-level-1 { padding-right: 0; font-weight: bold; color: #007bff; }
        .account-level-2 { padding-right: 20px; font-weight: 600; color: #28a745; }
        .account-level-3 { padding-right: 40px; color: #ffc107; }
        .account-level-4 { padding-right: 60px; color: #6c757d; }
        .account-level-5 { padding-right: 80px; color: #17a2b8; }
        
        .balance-debit { color: #dc3545; }
        .balance-credit { color: #28a745; }
        .balance-zero { color: #6c757d; }
        
        .account-actions {
            white-space: nowrap;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الهيدر -->
        <header class="header">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-chart-line"></i> <?php echo $pageTitle; ?></h1>
                <div>
                    <?php if (hasPermission('accounts', 'add')): ?>
                    <button type="button" class="btn btn-primary" onclick="openAddModal()">
                        <i class="fas fa-plus"></i> إضافة حساب جديد
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </header>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <!-- جدول شجرة الحسابات -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tree"></i> شجرة الحسابات المحاسبية</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رمز الحساب</th>
                                <th>اسم الحساب</th>
                                <th>نوع الحساب</th>
                                <th>الحساب الأب</th>
                                <th>الرصيد الحالي</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody class="account-tree">
                            <?php if (!empty($accounts)): ?>
                                <?php foreach ($accounts as $account): ?>
                                <tr>
                                    <td class="account-level-<?php echo $account['account_level']; ?>">
                                        <?php echo htmlspecialchars($account['account_code']); ?>
                                    </td>
                                    <td class="account-level-<?php echo $account['account_level']; ?>">
                                        <?php echo str_repeat('└─ ', max(0, $account['account_level'] - 1)); ?>
                                        <?php echo htmlspecialchars($account['account_name']); ?>
                                        <?php if ($account['children_count'] > 0): ?>
                                            <small class="text-muted">(<?php echo $account['children_count']; ?> حساب فرعي)</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $types = [
                                            'asset' => 'أصول',
                                            'liability' => 'خصوم',
                                            'equity' => 'حقوق ملكية',
                                            'revenue' => 'إيرادات',
                                            'expense' => 'مصروفات'
                                        ];
                                        echo $types[$account['account_type']] ?? $account['account_type'];
                                        ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($account['parent_name']); ?></td>
                                    <td>
                                        <?php
                                        $balance = $account['current_balance'];
                                        $class = $balance > 0 ? 'balance-debit' : ($balance < 0 ? 'balance-credit' : 'balance-zero');
                                        ?>
                                        <span class="<?php echo $class; ?>">
                                            <?php echo formatMoney(abs($balance)); ?>
                                            <?php if ($balance != 0): ?>
                                                <?php echo $balance > 0 ? '(مدين)' : '(دائن)'; ?>
                                            <?php endif; ?>
                                        </span>
                                    </td>
                                    <td class="account-actions">
                                        <?php if (hasPermission('accounts', 'edit')): ?>
                                        <button type="button" class="btn btn-sm btn-warning" 
                                                onclick="openEditModal(<?php echo htmlspecialchars(json_encode($account)); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php endif; ?>
                                        
                                        <button type="button" class="btn btn-sm btn-info" 
                                                onclick="viewAccountDetails(<?php echo $account['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <?php if (hasPermission('accounts', 'delete') && $account['children_count'] == 0): ?>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="deleteAccount(<?php echo $account['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center">لا توجد حسابات مسجلة</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة حساب جديد -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addModal')">&times;</span>
            <h3><i class="fas fa-plus"></i> إضافة حساب جديد</h3>
            
            <form method="POST" action="">
                <input type="hidden" name="action" value="add">
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="account_code" class="form-label">رمز الحساب *</label>
                            <input type="text" name="account_code" id="account_code" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="account_type" class="form-label">نوع الحساب *</label>
                            <select name="account_type" id="account_type" class="form-select" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="asset">أصول</option>
                                <option value="liability">خصوم</option>
                                <option value="equity">حقوق ملكية</option>
                                <option value="revenue">إيرادات</option>
                                <option value="expense">مصروفات</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="account_name" class="form-label">اسم الحساب *</label>
                    <input type="text" name="account_name" id="account_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="account_name_en" class="form-label">اسم الحساب بالإنجليزية</label>
                    <input type="text" name="account_name_en" id="account_name_en" class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="parent_id" class="form-label">الحساب الأب</label>
                    <select name="parent_id" id="parent_id" class="form-select">
                        <option value="">حساب رئيسي</option>
                        <?php foreach ($parent_accounts as $parent): ?>
                        <option value="<?php echo $parent['id']; ?>">
                            <?php echo str_repeat('└─ ', max(0, $parent['account_level'] - 1)); ?>
                            <?php echo htmlspecialchars($parent['account_code'] . ' - ' . $parent['account_name']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="opening_balance" class="form-label">الرصيد الافتتاحي</label>
                    <input type="number" name="opening_balance" id="opening_balance" class="form-control" step="0.01" value="0">
                </div>
                
                <div class="form-group">
                    <label for="description" class="form-label">وصف الحساب</label>
                    <textarea name="description" id="description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-group text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الحساب
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل الحساب -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('editModal')">&times;</span>
            <h3><i class="fas fa-edit"></i> تعديل الحساب</h3>
            
            <form method="POST" action="">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="account_id" id="edit_account_id">
                
                <div class="form-group">
                    <label for="edit_account_code" class="form-label">رمز الحساب</label>
                    <input type="text" name="account_code" id="edit_account_code" class="form-control" readonly>
                </div>
                
                <div class="form-group">
                    <label for="edit_account_name" class="form-label">اسم الحساب *</label>
                    <input type="text" name="account_name" id="edit_account_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_account_name_en" class="form-label">اسم الحساب بالإنجليزية</label>
                    <input type="text" name="account_name_en" id="edit_account_name_en" class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="edit_description" class="form-label">وصف الحساب</label>
                    <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-group text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../../assets/js/main.js"></script>
    
    <script>
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }
        
        function openEditModal(account) {
            document.getElementById('edit_account_id').value = account.id;
            document.getElementById('edit_account_code').value = account.account_code;
            document.getElementById('edit_account_name').value = account.account_name;
            document.getElementById('edit_account_name_en').value = account.account_name_en || '';
            document.getElementById('edit_description').value = account.description || '';
            
            document.getElementById('editModal').style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        function viewAccountDetails(accountId) {
            window.open(`account_details.php?id=${accountId}`, '_blank');
        }
        
        function deleteAccount(accountId) {
            if (confirm('هل أنت متأكد من حذف هذا الحساب؟\nسيتم حذف جميع القيود المرتبطة به.')) {
                window.location.href = `delete_account.php?id=${accountId}`;
            }
        }
        
        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>

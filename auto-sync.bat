@echo off
chcp 65001 >nul
title مزامنة تلقائية - نظام إدارة المخبز
color 0B

echo.
echo ========================================
echo      المزامنة التلقائية للملفات
echo    نظام إدارة المخبز المحاسبي
echo ========================================
echo.

REM تحديد المجلدات
set SOURCE_DIR=%~dp0
set TARGET_DIR=C:\laragon\www\bakery-system\

echo 📁 مراقبة المجلد: %SOURCE_DIR%
echo 📁 المزامنة مع: %TARGET_DIR%
echo.

REM التحقق من وجود Laragon
if not exist "C:\laragon\www\" (
    echo ❌ خطأ: Laragon غير موجود
    echo    يرجى تثبيت Laragon أولاً
    pause
    exit /b 1
)

REM إنشاء مجلد الهدف إذا لم يكن موجوداً
if not exist "%TARGET_DIR%" (
    echo 📁 إنشاء مجلد المشروع...
    mkdir "%TARGET_DIR%"
    echo ✅ تم إنشاء: %TARGET_DIR%
)

echo 🔄 بدء المزامنة التلقائية...
echo 💡 اضغط Ctrl+C للتوقف
echo.

REM حفظ آخر وقت تعديل للملفات
set "LAST_SYNC_FILE=%TEMP%\bakery_last_sync.txt"

:SYNC_LOOP

REM فحص التغييرات في ملفات PHP
for %%f in ("%SOURCE_DIR%*.php") do (
    call :CHECK_AND_SYNC "%%f" "%%~nxf"
)

REM فحص التغييرات في ملفات HTML
for %%f in ("%SOURCE_DIR%*.html") do (
    call :CHECK_AND_SYNC "%%f" "%%~nxf"
)

REM فحص التغييرات في ملفات CSS
for %%f in ("%SOURCE_DIR%*.css") do (
    call :CHECK_AND_SYNC "%%f" "%%~nxf"
)

REM فحص التغييرات في ملفات JS
for %%f in ("%SOURCE_DIR%*.js") do (
    call :CHECK_AND_SYNC "%%f" "%%~nxf"
)

REM فحص التغييرات في ملفات أخرى
for %%f in ("%SOURCE_DIR%*.json" "%SOURCE_DIR%*.sql" "%SOURCE_DIR%*.txt") do (
    if exist "%%f" (
        call :CHECK_AND_SYNC "%%f" "%%~nxf"
    )
)

REM انتظار 3 ثوان قبل الفحص التالي
timeout /t 3 >nul

goto SYNC_LOOP

:CHECK_AND_SYNC
set "SOURCE_FILE=%~1"
set "FILE_NAME=%~2"
set "TARGET_FILE=%TARGET_DIR%%FILE_NAME%"

REM فحص إذا كان الملف المصدر أحدث من الهدف
if not exist "%TARGET_FILE%" (
    call :SYNC_FILE "%SOURCE_FILE%" "%TARGET_FILE%" "%FILE_NAME%"
    goto :eof
)

REM مقارنة تواريخ التعديل
for %%i in ("%SOURCE_FILE%") do set SOURCE_DATE=%%~ti
for %%i in ("%TARGET_FILE%") do set TARGET_DATE=%%~ti

if not "%SOURCE_DATE%"=="%TARGET_DATE%" (
    call :SYNC_FILE "%SOURCE_FILE%" "%TARGET_FILE%" "%FILE_NAME%"
)

goto :eof

:SYNC_FILE
set "SRC=%~1"
set "TGT=%~2"
set "NAME=%~3"

copy "%SRC%" "%TGT%" >nul 2>&1
if %ERRORLEVEL%==0 (
    echo [%TIME%] ✅ تم تحديث: %NAME%
) else (
    echo [%TIME%] ❌ فشل تحديث: %NAME%
)

goto :eof

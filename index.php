<?php
/**
 * الملف الرئيسي للنظام الهجين HTML+PHP
 * Main Hybrid HTML+PHP System File
 */

session_start();

// إعدادات النظام
define('APP_NAME', 'نظام إدارة المخبز المحاسبي');
define('APP_VERSION', '2.0.0');
define('APP_AUTHOR', 'أنوار سوفت');

// تضمين ملفات الإعدادات
require_once 'config/config.php';

// فحص حالة قاعدة البيانات
$dbStatus = 'disconnected';
$dbMessage = '';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // فحص وجود الجداول الأساسية
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        $dbStatus = 'connected';
        $dbMessage = 'قاعدة البيانات متصلة وجاهزة';
    } else {
        $dbStatus = 'setup_needed';
        $dbMessage = 'قاعدة البيانات تحتاج إعداد';
    }
} catch (PDOException $e) {
    $dbStatus = 'error';
    $dbMessage = 'خطأ في الاتصال: ' . $e->getMessage();
}

// فحص تسجيل الدخول
$isLoggedIn = isset($_SESSION['user_id']);
$currentUser = null;

if ($isLoggedIn) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$_SESSION['user_id']]);
        $currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$currentUser) {
            session_destroy();
            $isLoggedIn = false;
        }
    } catch (PDOException $e) {
        $isLoggedIn = false;
    }
}

// تحديد الصفحة المطلوبة
$page = $_GET['page'] ?? 'welcome';
$module = $_GET['module'] ?? '';
$action = $_GET['action'] ?? '';

// التوجيه حسب الحالة
if ($dbStatus === 'setup_needed' || $dbStatus === 'error') {
    $page = 'setup';
} elseif (!$isLoggedIn && $page !== 'welcome' && $page !== 'login') {
    $page = 'login';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - <?php echo APP_VERSION; ?></title>
    
    <!-- الخطوط والأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    
    <!-- ملفات CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        /* الأنماط الأساسية للنظام الهجين */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .system-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .main-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            margin: 1rem;
            border-radius: 1rem;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-area {
            padding: 2rem;
            min-height: calc(100vh - 200px);
        }

        .welcome-screen {
            text-align: center;
            padding: 3rem;
        }

        .welcome-screen h1 {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .welcome-screen p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-connected {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .status-setup {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-success { background: linear-gradient(135deg, #28a745, #20c997); }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #fd7e14); color: #212529; }
        .btn-danger { background: linear-gradient(135deg, #dc3545, #e83e8c); }

        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
        }

        .loading.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .system-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .main-content {
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="loading" id="loadingScreen">
        <div>
            <div class="spinner"></div>
            <p style="color: white; margin-top: 1rem;">جاري التحميل...</p>
        </div>
    </div>

    <div class="system-container">
        <?php if ($isLoggedIn && $dbStatus === 'connected'): ?>
        <!-- الشريط الجانبي -->
        <div class="sidebar" id="sidebar">
            <?php include 'includes/sidebar.php'; ?>
        </div>
        <?php endif; ?>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- الرأس -->
            <div class="header">
                <div>
                    <h1><i class="fas fa-bread-slice"></i> <?php echo APP_NAME; ?></h1>
                    <div class="status-indicator status-<?php echo $dbStatus === 'connected' ? 'connected' : ($dbStatus === 'error' ? 'error' : 'setup'); ?>">
                        <i class="fas fa-<?php echo $dbStatus === 'connected' ? 'check-circle' : ($dbStatus === 'error' ? 'exclamation-triangle' : 'cog'); ?>"></i>
                        <?php echo $dbMessage; ?>
                    </div>
                </div>
                
                <div>
                    <?php if ($isLoggedIn): ?>
                    <span>مرحباً، <?php echo htmlspecialchars($currentUser['full_name']); ?></span>
                    <a href="?action=logout" class="btn btn-danger">
                        <i class="fas fa-sign-out-alt"></i> خروج
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- منطقة المحتوى -->
            <div class="content-area">
                <?php
                // تضمين الصفحة المطلوبة
                switch ($page) {
                    case 'welcome':
                        include 'pages/welcome.php';
                        break;
                    case 'setup':
                        include 'pages/setup.php';
                        break;
                    case 'login':
                        include 'pages/login.php';
                        break;
                    case 'dashboard':
                        include 'pages/dashboard.php';
                        break;
                    default:
                        if ($module && file_exists("modules/$module/index.php")) {
                            include "modules/$module/index.php";
                        } else {
                            include 'pages/404.php';
                        }
                        break;
                }
                ?>
            </div>
        </div>
    </div>

    <!-- ملفات JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // إعدادات النظام الهجين
        const SystemConfig = {
            appName: '<?php echo APP_NAME; ?>',
            version: '<?php echo APP_VERSION; ?>',
            isLoggedIn: <?php echo $isLoggedIn ? 'true' : 'false'; ?>,
            dbStatus: '<?php echo $dbStatus; ?>',
            currentPage: '<?php echo $page; ?>',
            currentModule: '<?php echo $module; ?>'
        };

        // وظائف النظام
        function showLoading() {
            document.getElementById('loadingScreen').classList.add('show');
        }

        function hideLoading() {
            document.getElementById('loadingScreen').classList.remove('show');
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('collapsed');
            }
        }

        // معالجة الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ في النظام:', e.error);
        });

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🍞 نظام إدارة المخبز جاهز!');
            console.log('📊 الحالة:', SystemConfig);
            
            // إخفاء شاشة التحميل
            hideLoading();
        });

        <?php if (isset($_GET['action']) && $_GET['action'] === 'logout'): ?>
        // تسجيل الخروج
        fetch('logout.php', { method: 'POST' })
            .then(() => {
                window.location.href = '?page=welcome';
            });
        <?php endif; ?>
    </script>
</body>
</html>

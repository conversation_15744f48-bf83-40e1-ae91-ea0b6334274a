<?php
/**
 * ملف التثبيت التلقائي للنظام
 * Automatic Installation Script
 */

// التحقق من وجود ملف التكوين
if (file_exists('config/database.php')) {
    $config_exists = true;
    require_once 'config/database.php';
} else {
    $config_exists = false;
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من متطلبات النظام
            $step = 2;
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_name = $_POST['db_name'] ?? 'bakery_system';
            $db_user = $_POST['db_user'] ?? '';
            $db_pass = $_POST['db_pass'] ?? '';
            
            if (empty($db_user)) {
                $error = 'اسم المستخدم لقاعدة البيانات مطلوب';
            } else {
                try {
                    // اختبار الاتصال
                    $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // إنشاء قاعدة البيانات إذا لم تكن موجودة
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `$db_name`");
                    
                    // إنشاء ملف التكوين
                    $config_content = "<?php\n";
                    $config_content .= "/**\n * إعدادات قاعدة البيانات\n * Database Configuration\n */\n\n";
                    $config_content .= "// إعدادات قاعدة البيانات\n";
                    $config_content .= "define('DB_HOST', '$db_host');\n";
                    $config_content .= "define('DB_NAME', '$db_name');\n";
                    $config_content .= "define('DB_USER', '$db_user');\n";
                    $config_content .= "define('DB_PASS', '$db_pass');\n";
                    $config_content .= "define('DB_CHARSET', 'utf8mb4');\n\n";
                    $config_content .= file_get_contents('config/database.php.example');
                    
                    if (!is_dir('config')) {
                        mkdir('config', 0755, true);
                    }
                    
                    file_put_contents('config/database.php', $config_content);
                    
                    $step = 3;
                    $success = 'تم إنشاء قاعدة البيانات وملف التكوين بنجاح';
                    
                } catch (PDOException $e) {
                    $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
                }
            }
            break;
            
        case 3:
            // تثبيت الجداول والبيانات الأولية
            try {
                require_once 'config/database.php';
                $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // تنفيذ ملفات SQL
                $sql_files = [
                    'database/bakery_system.sql',
                    'database/inventory_invoices.sql',
                    'database/initial_data.sql'
                ];
                
                foreach ($sql_files as $file) {
                    if (file_exists($file)) {
                        $sql = file_get_contents($file);
                        $statements = explode(';', $sql);
                        
                        foreach ($statements as $statement) {
                            $statement = trim($statement);
                            if (!empty($statement)) {
                                $pdo->exec($statement);
                            }
                        }
                    }
                }
                
                $step = 4;
                $success = 'تم تثبيت قاعدة البيانات بنجاح';
                
            } catch (Exception $e) {
                $error = 'خطأ في تثبيت قاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 4:
            // إنشاء مستخدم المدير
            $admin_username = $_POST['admin_username'] ?? 'admin';
            $admin_password = $_POST['admin_password'] ?? '';
            $admin_name = $_POST['admin_name'] ?? 'مدير النظام';
            $admin_email = $_POST['admin_email'] ?? '';
            
            if (empty($admin_password)) {
                $error = 'كلمة مرور المدير مطلوبة';
            } else {
                try {
                    require_once 'config/database.php';
                    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // تحديث بيانات المدير
                    $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                    
                    $stmt = $pdo->prepare("UPDATE users SET username = ?, password = ?, full_name = ?, email = ? WHERE id = 1");
                    $stmt->execute([$admin_username, $hashed_password, $admin_name, $admin_email]);
                    
                    $step = 5;
                    $success = 'تم إنشاء حساب المدير بنجاح';
                    
                } catch (Exception $e) {
                    $error = 'خطأ في إنشاء حساب المدير: ' . $e->getMessage();
                }
            }
            break;
    }
}

// فحص متطلبات النظام
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'JSON Extension' => extension_loaded('json'),
        'MBString Extension' => extension_loaded('mbstring'),
        'Config Directory Writable' => is_writable(dirname(__FILE__)) || is_writable('config'),
        'Uploads Directory Writable' => is_writable('uploads') || is_writable(dirname(__FILE__))
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
$all_requirements_met = !in_array(false, $requirements);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة المخبز</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .install-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
            margin: 20px;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }
        
        .step {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            background: #f8f9fa;
            margin: 0 0.25rem;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        .step.active {
            background: #007bff;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .requirements-list {
            list-style: none;
        }
        
        .requirements-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .requirement-ok {
            color: #28a745;
        }
        
        .requirement-fail {
            color: #dc3545;
        }
        
        .text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🥖 تثبيت نظام إدارة المخبز</h1>
            <p>مرحباً بك في معالج التثبيت</p>
        </div>
        
        <div class="install-body">
            <!-- مؤشر الخطوات -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : ''; ?>">1. المتطلبات</div>
                <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : ''; ?>">2. قاعدة البيانات</div>
                <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : ''; ?>">3. التثبيت</div>
                <div class="step <?php echo $step >= 4 ? ($step == 4 ? 'active' : 'completed') : ''; ?>">4. المدير</div>
                <div class="step <?php echo $step >= 5 ? 'active' : ''; ?>">5. الانتهاء</div>
            </div>
            
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success); ?>
            </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
            <!-- الخطوة 1: فحص المتطلبات -->
            <h3>فحص متطلبات النظام</h3>
            <ul class="requirements-list">
                <?php foreach ($requirements as $requirement => $status): ?>
                <li class="<?php echo $status ? 'requirement-ok' : 'requirement-fail'; ?>">
                    <?php echo $status ? '✓' : '✗'; ?> <?php echo $requirement; ?>
                </li>
                <?php endforeach; ?>
            </ul>
            
            <?php if ($all_requirements_met): ?>
            <div class="text-center">
                <form method="POST" action="?step=2">
                    <button type="submit" class="btn btn-primary">متابعة إلى الخطوة التالية</button>
                </form>
            </div>
            <?php else: ?>
            <div class="alert alert-danger">
                يرجى التأكد من توفر جميع المتطلبات قبل المتابعة
            </div>
            <?php endif; ?>
            
            <?php elseif ($step == 2): ?>
            <!-- الخطوة 2: إعداد قاعدة البيانات -->
            <h3>إعداد قاعدة البيانات</h3>
            <form method="POST" action="?step=2">
                <div class="form-group">
                    <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                    <input type="text" name="db_host" id="db_host" class="form-control" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                    <input type="text" name="db_name" id="db_name" class="form-control" value="bakery_system" required>
                </div>
                
                <div class="form-group">
                    <label for="db_user" class="form-label">اسم المستخدم</label>
                    <input type="text" name="db_user" id="db_user" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="db_pass" class="form-label">كلمة المرور</label>
                    <input type="password" name="db_pass" id="db_pass" class="form-control">
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">إنشاء قاعدة البيانات</button>
                </div>
            </form>
            
            <?php elseif ($step == 3): ?>
            <!-- الخطوة 3: تثبيت الجداول -->
            <h3>تثبيت قاعدة البيانات</h3>
            <p>سيتم الآن إنشاء جميع الجداول والبيانات الأولية...</p>
            
            <form method="POST" action="?step=3">
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">بدء التثبيت</button>
                </div>
            </form>
            
            <?php elseif ($step == 4): ?>
            <!-- الخطوة 4: إنشاء حساب المدير -->
            <h3>إنشاء حساب المدير</h3>
            <form method="POST" action="?step=4">
                <div class="form-group">
                    <label for="admin_username" class="form-label">اسم المستخدم</label>
                    <input type="text" name="admin_username" id="admin_username" class="form-control" value="admin" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_password" class="form-label">كلمة المرور</label>
                    <input type="password" name="admin_password" id="admin_password" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_name" class="form-label">الاسم الكامل</label>
                    <input type="text" name="admin_name" id="admin_name" class="form-control" value="مدير النظام" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                    <input type="email" name="admin_email" id="admin_email" class="form-control">
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">إنشاء الحساب</button>
                </div>
            </form>
            
            <?php elseif ($step == 5): ?>
            <!-- الخطوة 5: الانتهاء -->
            <div class="text-center">
                <h3>🎉 تم التثبيت بنجاح!</h3>
                <p>تم تثبيت نظام إدارة المخبز بنجاح. يمكنك الآن البدء في استخدام النظام.</p>
                
                <div style="margin: 2rem 0;">
                    <a href="index.php" class="btn btn-success">دخول إلى النظام</a>
                </div>
                
                <div class="alert alert-success">
                    <strong>ملاحظة مهمة:</strong><br>
                    يرجى حذف ملف install.php من الخادم لأسباب أمنية.
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

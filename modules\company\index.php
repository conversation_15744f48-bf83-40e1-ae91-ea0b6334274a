<?php
/**
 * وحدة إدارة بيانات المنشأة
 * Company Settings Module
 */

// تضمين الملفات المطلوبة
require_once '../../config/database.php';
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/validation.php';

// التحقق من الصلاحيات
requirePermission('company', 'view');

$db = getDB();
$error = '';
$success = '';

// معالجة حفظ البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!hasPermission('company', 'edit')) {
        $error = 'ليس لديك صلاحية لتعديل بيانات المنشأة';
    } else {
        // التحقق من صحة البيانات
        $validator = validator($_POST);
        $validator->required('company_name', 'اسم المنشأة مطلوب')
                 ->maxLength('company_name', 255)
                 ->maxLength('company_name_en', 255)
                 ->email('email', 'البريد الإلكتروني غير صحيح')
                 ->maxLength('phone', 50)
                 ->maxLength('mobile', 50)
                 ->numeric('tax_rate', 'نسبة الضريبة يجب أن تكون رقم')
                 ->min('tax_rate', 0, 'نسبة الضريبة لا يمكن أن تكون سالبة')
                 ->max('tax_rate', 100, 'نسبة الضريبة لا يمكن أن تزيد عن 100%')
                 ->date('fiscal_year_start', 'Y-m-d', 'تاريخ بداية السنة المالية غير صحيح');
        
        if (!$validator->hasErrors()) {
            try {
                $db->beginTransaction();
                
                // التحقق من وجود سجل للمنشأة
                $existingCompany = $db->selectOne("SELECT id FROM company_settings LIMIT 1");
                
                $data = [
                    'company_name' => cleanInput($_POST['company_name']),
                    'company_name_en' => cleanInput($_POST['company_name_en']),
                    'address' => cleanInput($_POST['address']),
                    'phone' => cleanInput($_POST['phone']),
                    'mobile' => cleanInput($_POST['mobile']),
                    'email' => cleanInput($_POST['email']),
                    'website' => cleanInput($_POST['website']),
                    'tax_number' => cleanInput($_POST['tax_number']),
                    'commercial_register' => cleanInput($_POST['commercial_register']),
                    'currency_code' => cleanInput($_POST['currency_code']) ?: 'YER',
                    'currency_name' => cleanInput($_POST['currency_name']) ?: 'ريال يمني',
                    'currency_symbol' => cleanInput($_POST['currency_symbol']) ?: 'ر.ي',
                    'decimal_places' => intval($_POST['decimal_places']) ?: 2,
                    'tax_rate' => floatval($_POST['tax_rate']) ?: 0,
                    'fiscal_year_start' => $_POST['fiscal_year_start'] ?: null
                ];
                
                if ($existingCompany) {
                    // تحديث البيانات الموجودة
                    $query = "UPDATE company_settings SET " .
                            "company_name = ?, company_name_en = ?, address = ?, phone = ?, mobile = ?, " .
                            "email = ?, website = ?, tax_number = ?, commercial_register = ?, " .
                            "currency_code = ?, currency_name = ?, currency_symbol = ?, decimal_places = ?, " .
                            "tax_rate = ?, fiscal_year_start = ?, updated_at = NOW() " .
                            "WHERE id = ?";
                    
                    $params = array_values($data);
                    $params[] = $existingCompany['id'];
                    
                    $db->update($query, $params);
                    
                    logUserActivity('update', 'company_settings', $existingCompany['id'], null, $data);
                } else {
                    // إدراج بيانات جديدة
                    $query = "INSERT INTO company_settings (" . implode(', ', array_keys($data)) . ") VALUES (" . 
                            str_repeat('?,', count($data) - 1) . "?)";
                    
                    $companyId = $db->insert($query, array_values($data));
                    
                    logUserActivity('create', 'company_settings', $companyId, null, $data);
                }
                
                $db->commit();
                $success = 'تم حفظ بيانات المنشأة بنجاح';
                
            } catch (Exception $e) {
                $db->rollback();
                $error = 'حدث خطأ في حفظ البيانات: ' . $e->getMessage();
            }
        } else {
            $error = $validator->getFirstError();
        }
    }
}

// جلب بيانات المنشأة الحالية
try {
    $companyData = $db->selectOne("SELECT * FROM company_settings LIMIT 1");
} catch (Exception $e) {
    $error = 'خطأ في تحميل بيانات المنشأة: ' . $e->getMessage();
}

$pageTitle = 'بيانات المنشأة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الهيدر -->
        <header class="header">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-building"></i> <?php echo $pageTitle; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../../index.php">الرئيسية</a></li>
                        <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
                    </ol>
                </nav>
            </div>
        </header>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <!-- نموذج بيانات المنشأة -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-edit"></i> تحديث بيانات المنشأة</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="" enctype="multipart/form-data">
                    <div class="row">
                        <!-- البيانات الأساسية -->
                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-info-circle"></i> البيانات الأساسية</h6>
                            
                            <div class="form-group">
                                <label for="company_name" class="form-label">اسم المنشأة *</label>
                                <input type="text" 
                                       name="company_name" 
                                       id="company_name" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['company_name'] ?? ''); ?>"
                                       required>
                            </div>
                            
                            <div class="form-group">
                                <label for="company_name_en" class="form-label">اسم المنشأة بالإنجليزية</label>
                                <input type="text" 
                                       name="company_name_en" 
                                       id="company_name_en" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['company_name_en'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea name="address" 
                                          id="address" 
                                          class="form-control" 
                                          rows="3"><?php echo htmlspecialchars($companyData['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone" class="form-label">الهاتف</label>
                                <input type="text" 
                                       name="phone" 
                                       id="phone" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['phone'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="mobile" class="form-label">الجوال</label>
                                <input type="text" 
                                       name="mobile" 
                                       id="mobile" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['mobile'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <!-- معلومات الاتصال والإعدادات -->
                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-cog"></i> معلومات الاتصال والإعدادات</h6>
                            
                            <div class="form-group">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" 
                                       name="email" 
                                       id="email" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['email'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="website" class="form-label">الموقع الإلكتروني</label>
                                <input type="url" 
                                       name="website" 
                                       id="website" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['website'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" 
                                       name="tax_number" 
                                       id="tax_number" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['tax_number'] ?? ''); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="commercial_register" class="form-label">السجل التجاري</label>
                                <input type="text" 
                                       name="commercial_register" 
                                       id="commercial_register" 
                                       class="form-control" 
                                       value="<?php echo htmlspecialchars($companyData['commercial_register'] ?? ''); ?>">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="currency_name" class="form-label">اسم العملة</label>
                                        <input type="text" 
                                               name="currency_name" 
                                               id="currency_name" 
                                               class="form-control" 
                                               value="<?php echo htmlspecialchars($companyData['currency_name'] ?? 'ريال يمني'); ?>"
                                               readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="currency_symbol" class="form-label">رمز العملة</label>
                                        <input type="text" 
                                               name="currency_symbol" 
                                               id="currency_symbol" 
                                               class="form-control" 
                                               value="<?php echo htmlspecialchars($companyData['currency_symbol'] ?? 'ر.ي'); ?>"
                                               readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="decimal_places" class="form-label">عدد الخانات العشرية</label>
                                <select name="decimal_places" id="decimal_places" class="form-select">
                                    <option value="0" <?php echo ($companyData['decimal_places'] ?? 2) == 0 ? 'selected' : ''; ?>>0</option>
                                    <option value="1" <?php echo ($companyData['decimal_places'] ?? 2) == 1 ? 'selected' : ''; ?>>1</option>
                                    <option value="2" <?php echo ($companyData['decimal_places'] ?? 2) == 2 ? 'selected' : ''; ?>>2</option>
                                    <option value="3" <?php echo ($companyData['decimal_places'] ?? 2) == 3 ? 'selected' : ''; ?>>3</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="tax_rate" class="form-label">نسبة الضريبة (%)</label>
                                <input type="number" 
                                       name="tax_rate" 
                                       id="tax_rate" 
                                       class="form-control" 
                                       min="0" 
                                       max="100" 
                                       step="0.01"
                                       value="<?php echo $companyData['tax_rate'] ?? 0; ?>">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="fiscal_year_start" class="form-label">بداية السنة المالية</label>
                                <input type="date" 
                                       name="fiscal_year_start" 
                                       id="fiscal_year_start" 
                                       class="form-control" 
                                       value="<?php echo $companyData['fiscal_year_start'] ?? ''; ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group text-center">
                        <?php if (hasPermission('company', 'edit')): ?>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i> حفظ البيانات
                        </button>
                        <?php endif; ?>
                        
                        <a href="../../index.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-arrow-left"></i> العودة للرئيسية
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- JavaScript Files -->
    <script src="../../assets/js/main.js"></script>
</body>
</html>

-- قاعدة بيانات نظام إدارة المخبز المحاسبي
-- Bakery Management System Database

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS bakery_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE bakery_system;

-- ===== جدول إعدادات الشركة =====
CREATE TABLE company_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL DEFAULT 'مخبز الأنوار',
    company_name_en VARCHAR(255) DEFAULT 'Al-Anwar Bakery',
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(100),
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    currency VARCHAR(10) DEFAULT 'YER',
    currency_symbol VARCHAR(10) DEFAULT 'ر.ي',
    logo_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===== جدول المستخدمين =====
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('admin', 'accountant', 'cashier', 'manager') DEFAULT 'cashier',
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===== شجرة الحسابات =====
CREATE TABLE chart_of_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(20) UNIQUE NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    account_name_en VARCHAR(100),
    parent_id INT NULL,
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    account_level INT DEFAULT 1,
    is_main_account BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    opening_balance DECIMAL(15,3) DEFAULT 0.000,
    current_balance DECIMAL(15,3) DEFAULT 0.000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES chart_of_accounts(id)
);

-- ===== الصناديق =====
CREATE TABLE cash_boxes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    account_id INT NOT NULL,
    opening_balance DECIMAL(15,3) DEFAULT 0.000,
    current_balance DECIMAL(15,3) DEFAULT 0.000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- ===== البنوك =====
CREATE TABLE banks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50),
    iban VARCHAR(50),
    account_id INT NOT NULL,
    opening_balance DECIMAL(15,3) DEFAULT 0.000,
    current_balance DECIMAL(15,3) DEFAULT 0.000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- ===== الموظفين =====
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_code VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    position VARCHAR(50),
    department VARCHAR(50),
    hire_date DATE,
    salary DECIMAL(10,2),
    phone VARCHAR(20),
    address TEXT,
    national_id VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== فئات المنتجات =====
CREATE TABLE product_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== المنتجات =====
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    category_id INT,
    unit VARCHAR(20) DEFAULT 'قطعة',
    cost_price DECIMAL(10,3),
    selling_price DECIMAL(10,3),
    stock_quantity DECIMAL(10,3) DEFAULT 0,
    min_stock_level DECIMAL(10,3) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id)
);

-- ===== العملاء =====
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    credit_limit DECIMAL(15,3) DEFAULT 0.000,
    current_balance DECIMAL(15,3) DEFAULT 0.000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== الموردين =====
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    current_balance DECIMAL(15,3) DEFAULT 0.000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== الفواتير =====
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(20) UNIQUE NOT NULL,
    invoice_type ENUM('sale', 'purchase', 'return_sale', 'return_purchase') NOT NULL,
    customer_id INT NULL,
    supplier_id INT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(15,3) NOT NULL,
    tax_amount DECIMAL(15,3) DEFAULT 0.000,
    discount_amount DECIMAL(15,3) DEFAULT 0.000,
    total_amount DECIMAL(15,3) NOT NULL,
    paid_amount DECIMAL(15,3) DEFAULT 0.000,
    remaining_amount DECIMAL(15,3) DEFAULT 0.000,
    status ENUM('draft', 'confirmed', 'paid', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===== تفاصيل الفواتير =====
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,3) NOT NULL,
    total_price DECIMAL(15,3) NOT NULL,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- ===== القيود المحاسبية =====
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_number VARCHAR(20) UNIQUE NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type VARCHAR(50),
    reference_id INT,
    total_debit DECIMAL(15,3) NOT NULL,
    total_credit DECIMAL(15,3) NOT NULL,
    status ENUM('draft', 'posted') DEFAULT 'draft',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===== تفاصيل القيود المحاسبية =====
CREATE TABLE journal_entry_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_id INT NOT NULL,
    account_id INT NOT NULL,
    debit_amount DECIMAL(15,3) DEFAULT 0.000,
    credit_amount DECIMAL(15,3) DEFAULT 0.000,
    description TEXT,
    FOREIGN KEY (entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- ===== المدفوعات =====
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(20) UNIQUE NOT NULL,
    payment_type ENUM('receipt', 'payment') NOT NULL,
    payment_method ENUM('cash', 'bank', 'check') NOT NULL,
    amount DECIMAL(15,3) NOT NULL,
    payment_date DATE NOT NULL,
    customer_id INT NULL,
    supplier_id INT NULL,
    cash_box_id INT NULL,
    bank_id INT NULL,
    reference_number VARCHAR(50),
    description TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
    FOREIGN KEY (bank_id) REFERENCES banks(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===== الأصول الثابتة =====
CREATE TABLE fixed_assets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    asset_code VARCHAR(20) UNIQUE NOT NULL,
    asset_name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    purchase_date DATE,
    purchase_cost DECIMAL(15,3),
    useful_life_years INT,
    depreciation_method ENUM('straight_line', 'declining_balance') DEFAULT 'straight_line',
    accumulated_depreciation DECIMAL(15,3) DEFAULT 0.000,
    book_value DECIMAL(15,3),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== إعدادات النظام =====
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'text',
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===== سجل النشاطات =====
CREATE TABLE activity_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- ===== الفهارس لتحسين الأداء =====
CREATE INDEX idx_invoices_date ON invoices(invoice_date);
CREATE INDEX idx_invoices_customer ON invoices(customer_id);
CREATE INDEX idx_invoices_supplier ON invoices(supplier_id);
CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_payments_date ON payments(payment_date);
CREATE INDEX idx_products_code ON products(product_code);
CREATE INDEX idx_customers_code ON customers(customer_code);
CREATE INDEX idx_suppliers_code ON suppliers(supplier_code);
CREATE INDEX idx_activity_log_user ON activity_log(user_id);
CREATE INDEX idx_activity_log_date ON activity_log(created_at);

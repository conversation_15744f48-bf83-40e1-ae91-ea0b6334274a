-- قاعدة بيانات النظام المحاسبي للمخبز
-- Bakery Accounting System Database

CREATE DATABASE IF NOT EXISTS bakery_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE bakery_system;

-- ===================================
-- جدول إعدادات المنشأة
-- ===================================
CREATE TABLE company_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL COMMENT 'اسم المنشأة',
    company_name_en VARCHAR(255) COMMENT 'اسم المنشأة بالإنجليزية',
    address TEXT COMMENT 'العنوان',
    phone VARCHAR(50) COMMENT 'الهاتف',
    mobile VARCHAR(50) COMMENT 'الجوال',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    website VARCHAR(100) COMMENT 'الموقع الإلكتروني',
    logo VARCHAR(255) COMMENT 'شعار المنشأة',
    tax_number VARCHAR(50) COMMENT 'الرقم الضريبي',
    commercial_register VARCHAR(50) COMMENT 'السجل التجاري',
    currency_code VARCHAR(3) DEFAULT 'YER' COMMENT 'رمز العملة',
    currency_name VARCHAR(50) DEFAULT 'ريال يمني' COMMENT 'اسم العملة',
    currency_symbol VARCHAR(10) DEFAULT 'ر.ي' COMMENT 'رمز العملة',
    decimal_places INT DEFAULT 2 COMMENT 'عدد الخانات العشرية',
    tax_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
    fiscal_year_start DATE COMMENT 'بداية السنة المالية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================
-- جدول إعدادات النظام
-- ===================================
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL COMMENT 'مفتاح الإعداد',
    setting_value TEXT COMMENT 'قيمة الإعداد',
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT 'نوع الإعداد',
    description TEXT COMMENT 'وصف الإعداد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================
-- جدول المستخدمين
-- ===================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT 'اسم المستخدم',
    password VARCHAR(255) NOT NULL COMMENT 'كلمة المرور',
    full_name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    role ENUM('admin', 'manager', 'accountant', 'cashier', 'user') DEFAULT 'user' COMMENT 'الدور',
    permissions JSON COMMENT 'الصلاحيات',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    last_login TIMESTAMP NULL COMMENT 'آخر تسجيل دخول',
    created_by INT COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول سجل نشاطات المستخدمين
-- ===================================
CREATE TABLE user_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    action VARCHAR(100) NOT NULL COMMENT 'العملية',
    table_name VARCHAR(50) COMMENT 'اسم الجدول',
    record_id INT COMMENT 'معرف السجل',
    old_data JSON COMMENT 'البيانات القديمة',
    new_data JSON COMMENT 'البيانات الجديدة',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    user_agent TEXT COMMENT 'معلومات المتصفح',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- ===================================
-- جدول شجرة الحسابات
-- ===================================
CREATE TABLE chart_of_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز الحساب',
    account_name VARCHAR(255) NOT NULL COMMENT 'اسم الحساب',
    account_name_en VARCHAR(255) COMMENT 'اسم الحساب بالإنجليزية',
    parent_id INT NULL COMMENT 'الحساب الأب',
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL COMMENT 'نوع الحساب',
    account_level INT NOT NULL DEFAULT 1 COMMENT 'مستوى الحساب',
    is_main BOOLEAN DEFAULT FALSE COMMENT 'حساب رئيسي',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    opening_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الافتتاحي',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الحالي',
    description TEXT COMMENT 'وصف الحساب',
    created_by INT COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول القيود المحاسبية
-- ===================================
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_number VARCHAR(20) UNIQUE NOT NULL COMMENT 'رقم القيد',
    entry_date DATE NOT NULL COMMENT 'تاريخ القيد',
    reference_type ENUM('manual', 'invoice', 'voucher', 'salary', 'transfer', 'opening', 'depreciation') NOT NULL COMMENT 'نوع المرجع',
    reference_id INT COMMENT 'معرف المرجع',
    description TEXT NOT NULL COMMENT 'وصف القيد',
    total_debit DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'إجمالي المدين',
    total_credit DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'إجمالي الدائن',
    is_balanced BOOLEAN DEFAULT TRUE COMMENT 'متوازن',
    is_posted BOOLEAN DEFAULT FALSE COMMENT 'مرحل',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول تفاصيل القيود المحاسبية
-- ===================================
CREATE TABLE journal_entry_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_id INT NOT NULL COMMENT 'معرف القيد',
    account_id INT NOT NULL COMMENT 'معرف الحساب',
    debit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'المبلغ المدين',
    credit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'المبلغ الدائن',
    description TEXT COMMENT 'وصف البند',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- ===================================
-- جدول الصناديق
-- ===================================
CREATE TABLE cash_boxes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    box_name VARCHAR(100) NOT NULL COMMENT 'اسم الصندوق',
    box_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز الصندوق',
    account_id INT NOT NULL COMMENT 'معرف الحساب المرتبط',
    responsible_user_id INT COMMENT 'المستخدم المسؤول',
    opening_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الافتتاحي',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الحالي',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    description TEXT COMMENT 'وصف الصندوق',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (responsible_user_id) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول البنوك
-- ===================================
CREATE TABLE banks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bank_name VARCHAR(100) NOT NULL COMMENT 'اسم البنك',
    account_number VARCHAR(50) NOT NULL COMMENT 'رقم الحساب',
    account_name VARCHAR(100) NOT NULL COMMENT 'اسم الحساب',
    account_id INT NOT NULL COMMENT 'معرف الحساب المرتبط',
    branch VARCHAR(100) COMMENT 'الفرع',
    iban VARCHAR(50) COMMENT 'رقم الآيبان',
    swift_code VARCHAR(20) COMMENT 'رمز السويفت',
    opening_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الافتتاحي',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'الرصيد الحالي',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    description TEXT COMMENT 'وصف الحساب البنكي',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول الموظفين
-- ===================================
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'رمز الموظف',
    full_name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل',
    national_id VARCHAR(20) COMMENT 'رقم الهوية',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    address TEXT COMMENT 'العنوان',
    job_title VARCHAR(100) COMMENT 'المسمى الوظيفي',
    department VARCHAR(100) COMMENT 'القسم',
    hire_date DATE NOT NULL COMMENT 'تاريخ التعيين',
    basic_salary DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'الراتب الأساسي',
    allowances DECIMAL(10,2) DEFAULT 0.00 COMMENT 'البدلات',
    account_id INT COMMENT 'معرف الحساب المرتبط',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- جدول رواتب الموظفين
-- ===================================
CREATE TABLE employee_salaries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL COMMENT 'معرف الموظف',
    salary_month DATE NOT NULL COMMENT 'شهر الراتب',
    basic_salary DECIMAL(10,2) NOT NULL COMMENT 'الراتب الأساسي',
    allowances DECIMAL(10,2) DEFAULT 0.00 COMMENT 'البدلات',
    overtime_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT 'مبلغ الإضافي',
    deductions DECIMAL(10,2) DEFAULT 0.00 COMMENT 'الخصومات',
    advances DECIMAL(10,2) DEFAULT 0.00 COMMENT 'السلف',
    net_salary DECIMAL(10,2) NOT NULL COMMENT 'صافي الراتب',
    is_paid BOOLEAN DEFAULT FALSE COMMENT 'مدفوع',
    payment_date DATE COMMENT 'تاريخ الدفع',
    journal_entry_id INT COMMENT 'معرف القيد المحاسبي',
    notes TEXT COMMENT 'ملاحظات',
    created_by INT NOT NULL COMMENT 'أنشئ بواسطة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    UNIQUE KEY unique_employee_month (employee_id, salary_month)
);

<?php
/**
 * ملف تحسين الأداء
 * Performance Optimization
 */

// منع الوصول المباشر
if (!defined('APP_PATH')) {
    die('Access denied');
}

/**
 * فئة تحسين الأداء
 */
class Performance {
    
    private static $cache = array();
    private static $queryCache = array();
    private static $startTime;
    private static $queries = array();
    
    /**
     * بدء قياس الأداء
     */
    public static function start() {
        self::$startTime = microtime(true);
        
        // تفعيل ضغط الإخراج
        if (!ob_get_level() && extension_loaded('zlib')) {
            ob_start('ob_gzhandler');
        }
        
        // تعيين رؤوس التخزين المؤقت
        self::setCacheHeaders();
    }
    
    /**
     * انتهاء قياس الأداء
     */
    public static function end() {
        $endTime = microtime(true);
        $executionTime = $endTime - self::$startTime;
        
        // إضافة معلومات الأداء كتعليق HTML
        if (defined('DEBUG') && DEBUG) {
            echo "\n<!-- Performance Info:\n";
            echo "Execution Time: " . number_format($executionTime * 1000, 2) . " ms\n";
            echo "Memory Usage: " . self::formatBytes(memory_get_usage()) . "\n";
            echo "Peak Memory: " . self::formatBytes(memory_get_peak_usage()) . "\n";
            echo "Database Queries: " . count(self::$queries) . "\n";
            echo "-->\n";
        }
    }
    
    /**
     * تعيين رؤوس التخزين المؤقت
     */
    private static function setCacheHeaders() {
        // تخزين مؤقت للملفات الثابتة
        $extension = pathinfo($_SERVER['REQUEST_URI'], PATHINFO_EXTENSION);
        $staticFiles = array('css', 'js', 'jpg', 'jpeg', 'png', 'gif', 'ico', 'woff', 'woff2', 'ttf');
        
        if (in_array($extension, $staticFiles)) {
            $expires = 60 * 60 * 24 * 30; // 30 يوم
            header('Cache-Control: public, max-age=' . $expires);
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $expires) . ' GMT');
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($_SERVER['SCRIPT_FILENAME'])) . ' GMT');
        }
    }
    
    /**
     * تخزين مؤقت للبيانات
     */
    public static function cache($key, $data = null, $ttl = 3600) {
        if ($data === null) {
            // استرجاع من التخزين المؤقت
            if (isset(self::$cache[$key])) {
                $cached = self::$cache[$key];
                if ($cached['expires'] > time()) {
                    return $cached['data'];
                } else {
                    unset(self::$cache[$key]);
                }
            }
            return null;
        } else {
            // حفظ في التخزين المؤقت
            self::$cache[$key] = array(
                'data' => $data,
                'expires' => time() + $ttl
            );
            return $data;
        }
    }
    
    /**
     * تخزين مؤقت لاستعلامات قاعدة البيانات
     */
    public static function cacheQuery($sql, $params = array(), $ttl = 300) {
        $key = md5($sql . serialize($params));
        
        // البحث في التخزين المؤقت
        if (isset(self::$queryCache[$key])) {
            $cached = self::$queryCache[$key];
            if ($cached['expires'] > time()) {
                return $cached['data'];
            } else {
                unset(self::$queryCache[$key]);
            }
        }
        
        return null;
    }
    
    /**
     * حفظ نتيجة استعلام في التخزين المؤقت
     */
    public static function setCacheQuery($sql, $params, $result, $ttl = 300) {
        $key = md5($sql . serialize($params));
        self::$queryCache[$key] = array(
            'data' => $result,
            'expires' => time() + $ttl
        );
    }
    
    /**
     * تسجيل استعلام قاعدة البيانات
     */
    public static function logQuery($sql, $params = array(), $executionTime = 0) {
        if (defined('DEBUG') && DEBUG) {
            self::$queries[] = array(
                'sql' => $sql,
                'params' => $params,
                'time' => $executionTime
            );
        }
    }
    
    /**
     * ضغط CSS
     */
    public static function minifyCSS($css) {
        // إزالة التعليقات
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // إزالة المسافات الزائدة
        $css = str_replace(array("\r\n", "\r", "\n", "\t", '  ', '    ', '    '), '', $css);
        
        // إزالة المسافات حول الرموز
        $css = str_replace(array('; ', ' ;', ' {', '{ ', '} ', ' }', ': ', ' :', ', ', ' ,'), array(';', ';', '{', '{', '}', '}', ':', ':', ',', ','), $css);
        
        return trim($css);
    }
    
    /**
     * ضغط JavaScript
     */
    public static function minifyJS($js) {
        // إزالة التعليقات أحادية السطر
        $js = preg_replace('/\/\/.*$/m', '', $js);
        
        // إزالة التعليقات متعددة الأسطر
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // إزالة المسافات الزائدة
        $js = preg_replace('/\s+/', ' ', $js);
        
        // إزالة المسافات حول الرموز
        $js = str_replace(array(' = ', ' + ', ' - ', ' * ', ' / ', ' == ', ' != ', ' < ', ' > ', ' <= ', ' >= ', ' && ', ' || '), array('=', '+', '-', '*', '/', '==', '!=', '<', '>', '<=', '>=', '&&', '||'), $js);
        
        return trim($js);
    }
    
    /**
     * ضغط HTML
     */
    public static function minifyHTML($html) {
        // إزالة التعليقات HTML (عدا التعليقات الشرطية)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);
        
        // إزالة المسافات الزائدة
        $html = preg_replace('/\s+/', ' ', $html);
        
        // إزالة المسافات بين العلامات
        $html = preg_replace('/>\s+</', '><', $html);
        
        return trim($html);
    }
    
    /**
     * تحسين الصور
     */
    public static function optimizeImage($imagePath, $quality = 85) {
        if (!file_exists($imagePath)) {
            return false;
        }
        
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }
        
        $mimeType = $imageInfo['mime'];
        
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                imagejpeg($image, $imagePath, $quality);
                break;
                
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                imagepng($image, $imagePath, 9 - round($quality / 10));
                break;
                
            case 'image/gif':
                // GIF لا يحتاج تحسين
                break;
        }
        
        if (isset($image)) {
            imagedestroy($image);
        }
        
        return true;
    }
    
    /**
     * دمج ملفات CSS
     */
    public static function combineCSS($files, $outputFile) {
        $combined = '';
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                $css = file_get_contents($file);
                $combined .= self::minifyCSS($css) . "\n";
            }
        }
        
        file_put_contents($outputFile, $combined);
        return $outputFile;
    }
    
    /**
     * دمج ملفات JavaScript
     */
    public static function combineJS($files, $outputFile) {
        $combined = '';
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                $js = file_get_contents($file);
                $combined .= self::minifyJS($js) . ";\n";
            }
        }
        
        file_put_contents($outputFile, $combined);
        return $outputFile;
    }
    
    /**
     * تنسيق حجم الملف
     */
    private static function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * تنظيف التخزين المؤقت
     */
    public static function clearCache() {
        self::$cache = array();
        self::$queryCache = array();
    }
    
    /**
     * تنظيف التخزين المؤقت المنتهي الصلاحية
     */
    public static function cleanExpiredCache() {
        $now = time();
        
        foreach (self::$cache as $key => $item) {
            if ($item['expires'] <= $now) {
                unset(self::$cache[$key]);
            }
        }
        
        foreach (self::$queryCache as $key => $item) {
            if ($item['expires'] <= $now) {
                unset(self::$queryCache[$key]);
            }
        }
    }
    
    /**
     * إحصائيات الأداء
     */
    public static function getStats() {
        return array(
            'execution_time' => microtime(true) - self::$startTime,
            'memory_usage' => memory_get_usage(),
            'peak_memory' => memory_get_peak_usage(),
            'queries_count' => count(self::$queries),
            'cache_items' => count(self::$cache),
            'query_cache_items' => count(self::$queryCache)
        );
    }
    
    /**
     * تحسين قاعدة البيانات
     */
    public static function optimizeDatabase() {
        try {
            $db = getDB();
            
            // الحصول على جميع الجداول
            $tables = $db->select("SHOW TABLES");
            
            foreach ($tables as $table) {
                $tableName = array_values($table);
                $tableName = $tableName[0];
                
                // تحسين الجدول
                $db->execute("OPTIMIZE TABLE `{$tableName}`");
                
                // تحليل الجدول
                $db->execute("ANALYZE TABLE `{$tableName}`");
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Database optimization error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * ضغط الإخراج
     */
    public static function compressOutput($buffer) {
        // تطبيق تحسينات HTML
        if (strpos($buffer, '<html') !== false) {
            $buffer = self::minifyHTML($buffer);
        }
        
        return $buffer;
    }
}

// بدء قياس الأداء تلقائياً
Performance::start();

// تسجيل دالة التنظيف عند انتهاء السكريبت
register_shutdown_function(array('Performance', 'end'));

// تفعيل ضغط الإخراج
if (defined('ENABLE_OUTPUT_COMPRESSION') && ENABLE_OUTPUT_COMPRESSION) {
    ob_start(array('Performance', 'compressOutput'));
}
?>
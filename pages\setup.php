<?php
/**
 * صفحة إعداد النظام
 * System Setup Page
 */

$setupSteps = [];
$errors = [];
$warnings = [];
$success = false;

// معالجة طلب الإعداد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
    try {
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $setupSteps[] = '✅ تم الاتصال بـ MySQL بنجاح';
        
        // تنفيذ ملف إنشاء قاعدة البيانات
        if (file_exists('create-database.sql')) {
            $sql = file_get_contents('create-database.sql');
            $queries = array_filter(array_map('trim', explode(';', $sql)));
            
            $executedQueries = 0;
            foreach ($queries as $query) {
                if (!empty($query) && !preg_match('/^--/', $query)) {
                    try {
                        $pdo->exec($query);
                        $executedQueries++;
                    } catch (PDOException $e) {
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            $warnings[] = 'تحذير: ' . $e->getMessage();
                        }
                    }
                }
            }
            
            $setupSteps[] = "✅ تم تنفيذ ملف إنشاء قاعدة البيانات ($executedQueries استعلام)";
        }
        
        // الاتصال بقاعدة البيانات الجديدة
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // تنفيذ البيانات التجريبية
        if (file_exists('sample-data.sql')) {
            $sql = file_get_contents('sample-data.sql');
            $queries = array_filter(array_map('trim', explode(';', $sql)));
            
            $insertedCount = 0;
            foreach ($queries as $query) {
                if (!empty($query) && !preg_match('/^--/', $query)) {
                    try {
                        $pdo->exec($query);
                        if (stripos($query, 'INSERT') === 0) {
                            $insertedCount++;
                        }
                    } catch (PDOException $e) {
                        if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                            $warnings[] = 'تحذير في البيانات: ' . $e->getMessage();
                        }
                    }
                }
            }
            
            $setupSteps[] = "✅ تم إدراج البيانات التجريبية ($insertedCount عملية إدراج)";
        }
        
        $setupSteps[] = '🎉 تم إعداد النظام بنجاح!';
        $success = true;
        
    } catch (PDOException $e) {
        $errors[] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    } catch (Exception $e) {
        $errors[] = 'خطأ عام: ' . $e->getMessage();
    }
}
?>

<div class="card">
    <!-- رأس الصفحة -->
    <div style="text-align: center; margin-bottom: 2rem;">
        <div style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;">
            <i class="fas fa-cogs"></i>
        </div>
        <h2 style="color: #333; margin-bottom: 0.5rem;">إعداد النظام</h2>
        <p style="color: #666;">إعداد قاعدة البيانات والبيانات التجريبية</p>
    </div>

    <!-- حالة النظام -->
    <div style="background: #e3f2fd; border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 2rem;">
        <h4 style="color: #1976d2; margin-bottom: 1rem;">
            <i class="fas fa-info-circle"></i> حالة النظام الحالية
        </h4>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div>
                <strong>قاعدة البيانات:</strong>
                <span style="color: <?php echo $dbStatus === 'connected' ? '#28a745' : '#dc3545'; ?>;">
                    <?php echo $dbStatus === 'connected' ? 'متصلة' : 'غير متصلة'; ?>
                </span>
            </div>
            <div>
                <strong>الخادم:</strong> <?php echo DB_HOST; ?>
            </div>
            <div>
                <strong>اسم قاعدة البيانات:</strong> <?php echo DB_NAME; ?>
            </div>
            <div>
                <strong>المستخدم:</strong> <?php echo DB_USER; ?>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
    <!-- رسالة النجاح -->
    <div style="background: #d4edda; color: #155724; padding: 1.5rem; border-radius: 0.5rem; margin-bottom: 2rem; border-right: 4px solid #28a745;">
        <h4><i class="fas fa-check-circle"></i> تم إعداد النظام بنجاح!</h4>
        <p>جميع الجداول والبيانات التجريبية جاهزة للاستخدام.</p>
        
        <div style="margin-top: 1rem;">
            <a href="?page=login" class="btn btn-success">
                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
            </a>
            <a href="?page=welcome" class="btn" style="margin-right: 1rem;">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
    <!-- رسائل الخطأ -->
    <div style="background: #f8d7da; color: #721c24; padding: 1.5rem; border-radius: 0.5rem; margin-bottom: 2rem; border-right: 4px solid #dc3545;">
        <h4><i class="fas fa-exclamation-circle"></i> أخطاء في الإعداد:</h4>
        <ul style="margin: 0.5rem 0 0 1.5rem;">
            <?php foreach ($errors as $error): ?>
            <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if (!empty($warnings)): ?>
    <!-- رسائل التحذير -->
    <div style="background: #fff3cd; color: #856404; padding: 1.5rem; border-radius: 0.5rem; margin-bottom: 2rem; border-right: 4px solid #ffc107;">
        <h4><i class="fas fa-exclamation-triangle"></i> تحذيرات:</h4>
        <ul style="margin: 0.5rem 0 0 1.5rem;">
            <?php foreach ($warnings as $warning): ?>
            <li><?php echo htmlspecialchars($warning); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if (!empty($setupSteps)): ?>
    <!-- خطوات الإعداد -->
    <div style="background: #f8f9fa; border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 2rem;">
        <h4 style="color: #495057; margin-bottom: 1rem;">
            <i class="fas fa-list-check"></i> خطوات الإعداد المنجزة:
        </h4>
        <div style="max-height: 300px; overflow-y: auto;">
            <?php foreach ($setupSteps as $step): ?>
            <div style="padding: 0.5rem 0; border-bottom: 1px solid #dee2e6; font-family: monospace;">
                <?php echo $step; ?>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!$success): ?>
    <!-- نموذج الإعداد -->
    <div style="text-align: center;">
        <h3 style="color: #333; margin-bottom: 1rem;">هل أنت مستعد لإعداد النظام؟</h3>
        <p style="color: #666; margin-bottom: 2rem;">
            سيقوم النظام بإنشاء قاعدة البيانات وجميع الجداول المطلوبة مع البيانات التجريبية
        </p>
        
        <div style="background: #fff3cd; border-radius: 0.5rem; padding: 1rem; margin-bottom: 2rem;">
            <h5 style="color: #856404; margin-bottom: 0.5rem;">
                <i class="fas fa-exclamation-triangle"></i> تنبيه مهم:
            </h5>
            <p style="color: #856404; font-size: 0.9rem; margin: 0;">
                إذا كانت قاعدة البيانات موجودة مسبقاً، سيتم تحديثها وإضافة البيانات الجديدة فقط
            </p>
        </div>
        
        <form method="POST" id="setupForm">
            <button type="submit" name="setup" class="btn btn-warning" style="font-size: 1.1rem; padding: 1rem 2rem;" id="setupButton">
                <i class="fas fa-rocket"></i>
                <span id="setupButtonText">بدء الإعداد التلقائي</span>
            </button>
        </form>
        
        <div style="margin-top: 2rem;">
            <a href="?page=welcome" style="color: #667eea; text-decoration: none;">
                <i class="fas fa-arrow-right"></i> العودة للصفحة الرئيسية
            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- معلومات إضافية -->
    <div style="background: #e8f5e9; border-radius: 0.5rem; padding: 1.5rem; margin-top: 2rem;">
        <h4 style="color: #2e7d32; margin-bottom: 1rem;">
            <i class="fas fa-database"></i> ما سيتم إنشاؤه:
        </h4>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; font-size: 0.9rem;">
            <div>
                <strong>الجداول الأساسية:</strong>
                <ul style="margin: 0.5rem 0 0 1rem; color: #2e7d32;">
                    <li>المستخدمين والصلاحيات</li>
                    <li>إعدادات الشركة</li>
                    <li>شجرة الحسابات</li>
                    <li>الصناديق والبنوك</li>
                </ul>
            </div>
            
            <div>
                <strong>جداول العمليات:</strong>
                <ul style="margin: 0.5rem 0 0 1rem; color: #2e7d32;">
                    <li>الفواتير والمبيعات</li>
                    <li>المدفوعات والإيصالات</li>
                    <li>القيود المحاسبية</li>
                    <li>سجل النشاطات</li>
                </ul>
            </div>
            
            <div>
                <strong>البيانات التجريبية:</strong>
                <ul style="margin: 0.5rem 0 0 1rem; color: #2e7d32;">
                    <li>4 مستخدمين تجريبيين</li>
                    <li>25+ حساب محاسبي</li>
                    <li>14 منتج متنوع</li>
                    <li>عملاء وموردين</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// وظائف صفحة الإعداد
document.addEventListener('DOMContentLoaded', function() {
    const setupForm = document.getElementById('setupForm');
    const setupButton = document.getElementById('setupButton');
    const setupButtonText = document.getElementById('setupButtonText');
    
    if (setupForm) {
        setupForm.addEventListener('submit', function(e) {
            // تأكيد الإعداد
            if (!confirm('هل أنت متأكد من بدء عملية الإعداد؟\n\nسيتم إنشاء قاعدة البيانات والجداول والبيانات التجريبية.')) {
                e.preventDefault();
                return;
            }
            
            // تغيير حالة الزر
            setupButton.disabled = true;
            setupButtonText.textContent = 'جاري الإعداد...';
            setupButton.style.opacity = '0.7';
            
            // إضافة أيقونة التحميل
            const spinner = document.createElement('i');
            spinner.className = 'fas fa-spinner fa-spin';
            spinner.style.marginLeft = '0.5rem';
            setupButton.insertBefore(spinner, setupButtonText);
            
            // إظهار شاشة التحميل
            showLoading();
        });
    }
    
    console.log('⚙️ صفحة الإعداد جاهزة');
});

// إضافة تأثيرات بصرية
const style = document.createElement('style');
style.textContent = `
    .card {
        animation: slideInUp 0.6s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .setup-step {
        animation: fadeInLeft 0.5s ease-out;
    }
    
    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
`;
document.head.appendChild(style);

<?php if ($success): ?>
// إعادة توجيه تلقائية بعد النجاح
setTimeout(() => {
    if (confirm('تم إعداد النظام بنجاح!\n\nهل تريد الانتقال إلى صفحة تسجيل الدخول؟')) {
        window.location.href = '?page=login';
    }
}, 3000);
<?php endif; ?>
</script>

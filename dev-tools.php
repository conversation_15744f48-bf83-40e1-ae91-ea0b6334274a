<?php
/**
 * أدوات التطوير والإدارة
 * Development and Management Tools
 */

session_start();

// إعدادات التطوير
$devMode = isset($_GET['dev']) && $_GET['dev'] === '1';
$projectPath = __DIR__ . '/';
$backupPath = $projectPath . 'backups/';

// إنشاء مجلد النسخ الاحتياطية
if (!is_dir($backupPath)) {
    mkdir($backupPath, 0755, true);
}

// معالجة الطلبات
$action = $_GET['action'] ?? '';
$message = '';
$error = '';

switch ($action) {
    case 'backup':
        $backupFile = $backupPath . 'backup_' . date('Y-m-d_H-i-s') . '.zip';
        if (createBackup($projectPath, $backupFile)) {
            $message = 'تم إنشاء نسخة احتياطية: ' . basename($backupFile);
        } else {
            $error = 'فشل في إنشاء النسخة الاحتياطية';
        }
        break;
        
    case 'clear_cache':
        if (clearCache()) {
            $message = 'تم مسح الذاكرة المؤقتة';
        } else {
            $error = 'فشل في مسح الذاكرة المؤقتة';
        }
        break;
        
    case 'restart_services':
        $message = 'يرجى إعادة تشغيل Apache و MySQL من Laragon';
        break;
}

// دالة إنشاء نسخة احتياطية
function createBackup($source, $destination) {
    if (!extension_loaded('zip')) {
        return false;
    }
    
    $zip = new ZipArchive();
    if ($zip->open($destination, ZipArchive::CREATE) !== TRUE) {
        return false;
    }
    
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($files as $name => $file) {
        if (!$file->isDir()) {
            $filePath = $file->getRealPath();
            $relativePath = substr($filePath, strlen($source));
            
            // تجاهل مجلد النسخ الاحتياطية
            if (strpos($relativePath, 'backups') === false) {
                $zip->addFile($filePath, $relativePath);
            }
        }
    }
    
    return $zip->close();
}

// دالة مسح الذاكرة المؤقتة
function clearCache() {
    $cacheFiles = glob($projectPath . '*.tmp');
    foreach ($cacheFiles as $file) {
        unlink($file);
    }
    return true;
}

// معلومات المشروع
$projectInfo = [
    'Project Path' => $projectPath,
    'PHP Version' => phpversion(),
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد',
    'Current Time' => date('Y-m-d H:i:s'),
    'Memory Usage' => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',
    'Peak Memory' => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB'
];

// قائمة الملفات
$files = [];
$iterator = new DirectoryIterator($projectPath);
foreach ($iterator as $file) {
    if (!$file->isDot() && $file->isFile()) {
        $files[] = [
            'name' => $file->getFilename(),
            'size' => round($file->getSize() / 1024, 2) . ' KB',
            'modified' => date('Y-m-d H:i:s', $file->getMTime()),
            'extension' => $file->getExtension()
        ];
    }
}

// ترتيب الملفات حسب التاريخ
usort($files, function($a, $b) {
    return strcmp($b['modified'], $a['modified']);
});
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات التطوير - نظام إدارة المخبز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }

        .card-body {
            padding: 1rem;
        }

        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 0.375rem;
            margin: 0.25rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .btn-info { background: #17a2b8; }

        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-right-color: #28a745;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-right-color: #dc3545;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .path-display {
            background: #e3f2fd;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.9rem;
            direction: ltr;
            text-align: left;
            word-break: break-all;
        }

        .quick-links {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .file-icon {
            margin-left: 0.5rem;
        }

        .php { color: #777bb4; }
        .html { color: #e34c26; }
        .css { color: #1572b6; }
        .js { color: #f7df1e; }
        .sql { color: #336791; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> أدوات التطوير</h1>
            <p>إدارة وتطوير نظام إدارة المخبز</p>
            <div style="margin-top: 1rem;">
                <span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.9rem;">
                    <i class="fas fa-folder"></i> مجلد العمل النشط
                </span>
            </div>
        </div>
        
        <div class="content">
            <!-- الرسائل -->
            <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>

            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>

            <!-- روابط سريعة -->
            <div class="quick-links">
                <a href="laragon-app.php" class="btn btn-success">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                <a href="setup-database.php" class="btn btn-info">
                    <i class="fas fa-database"></i> إعداد قاعدة البيانات
                </a>
                <a href="check-system.php" class="btn btn-warning">
                    <i class="fas fa-stethoscope"></i> فحص النظام
                </a>
                <a href="http://localhost/phpmyadmin/" target="_blank" class="btn">
                    <i class="fas fa-external-link-alt"></i> phpMyAdmin
                </a>
            </div>

            <div class="grid">
                <!-- معلومات المشروع -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-info-circle"></i> معلومات المشروع
                    </div>
                    <div class="card-body">
                        <?php foreach ($projectInfo as $key => $value): ?>
                        <div class="info-item">
                            <span><?php echo $key; ?></span>
                            <span>
                                <?php if ($key === 'Project Path'): ?>
                                    <div class="path-display"><?php echo htmlspecialchars($value); ?></div>
                                <?php else: ?>
                                    <?php echo htmlspecialchars($value); ?>
                                <?php endif; ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- أدوات الإدارة -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cogs"></i> أدوات الإدارة
                    </div>
                    <div class="card-body">
                        <a href="?action=backup" class="btn btn-success">
                            <i class="fas fa-download"></i> نسخة احتياطية
                        </a>
                        
                        <a href="?action=clear_cache" class="btn btn-warning">
                            <i class="fas fa-broom"></i> مسح الذاكرة المؤقتة
                        </a>
                        
                        <button onclick="location.reload()" class="btn btn-info">
                            <i class="fas fa-sync-alt"></i> تحديث الصفحة
                        </button>
                        
                        <a href="?action=restart_services" class="btn btn-danger">
                            <i class="fas fa-power-off"></i> إعادة تشغيل الخدمات
                        </a>
                    </div>
                </div>
            </div>

            <!-- قائمة الملفات -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-folder-open"></i> ملفات المشروع (<?php echo count($files); ?> ملف)
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>الحجم</th>
                                <th>آخر تعديل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($files as $file): ?>
                            <tr>
                                <td>
                                    <i class="fas fa-file file-icon <?php echo $file['extension']; ?>"></i>
                                    <?php echo htmlspecialchars($file['name']); ?>
                                </td>
                                <td><?php echo $file['size']; ?></td>
                                <td><?php echo $file['modified']; ?></td>
                                <td>
                                    <?php if (in_array($file['extension'], ['php', 'html'])): ?>
                                    <a href="<?php echo $file['name']; ?>" target="_blank" class="btn btn-info" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- نصائح التطوير -->
            <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 0.5rem;">
                <h4><i class="fas fa-lightbulb"></i> نصائح التطوير:</h4>
                <ul>
                    <li><strong>العمل المباشر:</strong> اعمل مباشرة في مجلد Laragon لتجنب النسخ</li>
                    <li><strong>الحفظ التلقائي:</strong> استخدم محرر يدعم الحفظ التلقائي</li>
                    <li><strong>النسخ الاحتياطية:</strong> أنشئ نسخ احتياطية قبل التغييرات الكبيرة</li>
                    <li><strong>فحص الأخطاء:</strong> راقب سجلات الأخطاء في Laragon</li>
                    <li><strong>اختبار مستمر:</strong> اختبر التغييرات فور حفظها</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // تحديث تلقائي لمعلومات الملفات كل 30 ثانية
        setInterval(() => {
            // تحديث جزئي للصفحة
            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    // يمكن إضافة تحديث جزئي هنا
                });
        }, 30000);

        console.log('🛠️ أدوات التطوير نشطة');
        console.log('📁 مجلد العمل:', '<?php echo $projectPath; ?>');
    </script>
</body>
</html>

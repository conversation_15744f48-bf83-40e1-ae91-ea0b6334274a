<?php
/**
 * ملف الدوال المساعدة
 * Helper Functions
 */

/**
 * دالة للتحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * دالة للحصول على بيانات المستخدم الحالي
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $db = getDB();
        return $db->selectOne(
            "SELECT * FROM users WHERE id = ? AND is_active = 1",
            [$_SESSION['user_id']]
        );
    } catch (Exception $e) {
        return null;
    }
}

/**
 * دالة للتحقق من الصلاحيات
 */
function hasPermission($module, $action = 'view') {
    $user = getCurrentUser();
    if (!$user) {
        return false;
    }
    
    // المدير له جميع الصلاحيات
    if ($user['role'] === 'admin') {
        return true;
    }
    
    // التحقق من الصلاحيات المخصصة
    $permissions = json_decode($user['permissions'], true) ?: [];
    
    // إذا كان لديه صلاحية شاملة
    if (isset($permissions['all']) && $permissions['all']) {
        return true;
    }
    
    // التحقق من صلاحية الوحدة والعملية
    return isset($permissions[$module][$action]) && $permissions[$module][$action];
}

/**
 * دالة لإعادة التوجيه
 */
function redirect($url) {
    header("Location: $url");
    exit();
}

/**
 * دالة لإعادة التوجيه إلى صفحة تسجيل الدخول
 */
function redirectToLogin() {
    redirect('login.php');
}

/**
 * دالة للتحقق من الصلاحية وإعادة التوجيه
 */
function requirePermission($module, $action = 'view') {
    if (!isLoggedIn()) {
        redirectToLogin();
    }
    
    if (!hasPermission($module, $action)) {
        redirect('index.php?error=permission');
    }
}

/**
 * دالة لتسجيل نشاط المستخدم
 */
function logUserActivity($action, $table_name = null, $record_id = null, $old_data = null, $new_data = null) {
    if (!isLoggedIn()) {
        return false;
    }
    
    try {
        $db = getDB();
        return $db->insert(
            "INSERT INTO user_logs (user_id, action, table_name, record_id, old_data, new_data, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $_SESSION['user_id'],
                $action,
                $table_name,
                $record_id,
                $old_data ? json_encode($old_data) : null,
                $new_data ? json_encode($new_data) : null,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]
        );
    } catch (Exception $e) {
        return false;
    }
}

/**
 * دالة لإنشاء قيد محاسبي
 */
function createJournalEntry($description, $entries, $reference_type = 'manual', $reference_id = null) {
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    $db = getDB();
    
    try {
        $db->beginTransaction();
        
        // حساب إجمالي المدين والدائن
        $total_debit = 0;
        $total_credit = 0;
        
        foreach ($entries as $entry) {
            $total_debit += $entry['debit_amount'] ?? 0;
            $total_credit += $entry['credit_amount'] ?? 0;
        }
        
        // التحقق من التوازن
        if (abs($total_debit - $total_credit) > 0.01) {
            throw new Exception('القيد غير متوازن');
        }
        
        // توليد رقم القيد
        $entry_number = generateNumber(JOURNAL_PREFIX, 'journal_entries', 'entry_number');
        
        // إدراج القيد الرئيسي
        $entry_id = $db->insert(
            "INSERT INTO journal_entries (entry_number, entry_date, reference_type, reference_id, description, total_debit, total_credit, is_balanced, created_by) 
             VALUES (?, CURDATE(), ?, ?, ?, ?, ?, 1, ?)",
            [$entry_number, $reference_type, $reference_id, $description, $total_debit, $total_credit, $_SESSION['user_id']]
        );
        
        // إدراج تفاصيل القيد
        foreach ($entries as $entry) {
            $db->insert(
                "INSERT INTO journal_entry_details (entry_id, account_id, debit_amount, credit_amount, description) 
                 VALUES (?, ?, ?, ?, ?)",
                [
                    $entry_id,
                    $entry['account_id'],
                    $entry['debit_amount'] ?? 0,
                    $entry['credit_amount'] ?? 0,
                    $entry['description'] ?? ''
                ]
            );
            
            // تحديث رصيد الحساب
            updateAccountBalance($entry['account_id']);
        }
        
        $db->commit();
        
        // تسجيل النشاط
        logUserActivity('create_journal_entry', 'journal_entries', $entry_id, null, [
            'entry_number' => $entry_number,
            'description' => $description,
            'total_amount' => $total_debit
        ]);
        
        return $entry_id;
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

/**
 * دالة لتحديث رصيد الحساب
 */
function updateAccountBalance($account_id) {
    $db = getDB();
    
    // حساب الرصيد من تفاصيل القيود
    $result = $db->selectOne(
        "SELECT 
            COALESCE(SUM(debit_amount), 0) as total_debit,
            COALESCE(SUM(credit_amount), 0) as total_credit
         FROM journal_entry_details jed
         JOIN journal_entries je ON jed.entry_id = je.id
         WHERE jed.account_id = ? AND je.is_posted = 1",
        [$account_id]
    );
    
    // الحصول على نوع الحساب والرصيد الافتتاحي
    $account = $db->selectOne(
        "SELECT account_type, opening_balance FROM chart_of_accounts WHERE id = ?",
        [$account_id]
    );
    
    if (!$account) {
        throw new Exception('الحساب غير موجود');
    }
    
    $opening_balance = $account['opening_balance'];
    $total_debit = $result['total_debit'];
    $total_credit = $result['total_credit'];
    
    // حساب الرصيد حسب نوع الحساب
    switch ($account['account_type']) {
        case 'asset':
        case 'expense':
            $current_balance = $opening_balance + $total_debit - $total_credit;
            break;
        case 'liability':
        case 'equity':
        case 'revenue':
            $current_balance = $opening_balance + $total_credit - $total_debit;
            break;
        default:
            $current_balance = $opening_balance + $total_debit - $total_credit;
    }
    
    // تحديث الرصيد
    $db->update(
        "UPDATE chart_of_accounts SET current_balance = ? WHERE id = ?",
        [$current_balance, $account_id]
    );
    
    return $current_balance;
}

/**
 * دالة لتحديث رصيد الصندوق
 */
function updateCashBoxBalance($cash_box_id) {
    $db = getDB();
    
    // حساب الرصيد من السندات
    $result = $db->selectOne(
        "SELECT 
            COALESCE(SUM(CASE WHEN voucher_type = 'receipt' THEN amount ELSE 0 END), 0) as total_receipts,
            COALESCE(SUM(CASE WHEN voucher_type = 'payment' THEN amount ELSE 0 END), 0) as total_payments
         FROM vouchers 
         WHERE cash_box_id = ? AND is_posted = 1",
        [$cash_box_id]
    );
    
    // الحصول على الرصيد الافتتاحي
    $cash_box = $db->selectOne(
        "SELECT opening_balance FROM cash_boxes WHERE id = ?",
        [$cash_box_id]
    );
    
    if (!$cash_box) {
        throw new Exception('الصندوق غير موجود');
    }
    
    $current_balance = $cash_box['opening_balance'] + $result['total_receipts'] - $result['total_payments'];
    
    // تحديث الرصيد
    $db->update(
        "UPDATE cash_boxes SET current_balance = ? WHERE id = ?",
        [$current_balance, $cash_box_id]
    );
    
    return $current_balance;
}

/**
 * دالة لتحديث رصيد المخزون
 */
function updateItemStock($item_id) {
    $db = getDB();
    
    // حساب الرصيد من حركات المخزون
    $result = $db->selectOne(
        "SELECT 
            COALESCE(SUM(CASE WHEN movement_type IN ('in', 'production') THEN quantity ELSE 0 END), 0) as total_in,
            COALESCE(SUM(CASE WHEN movement_type IN ('out', 'transfer') THEN quantity ELSE 0 END), 0) as total_out
         FROM stock_movements 
         WHERE item_id = ?",
        [$item_id]
    );
    
    $current_stock = $result['total_in'] - $result['total_out'];
    
    // تحديث المخزون
    $db->update(
        "UPDATE items SET current_stock = ? WHERE id = ?",
        [$current_stock, $item_id]
    );
    
    return $current_stock;
}

/**
 * دالة لإنشاء حركة مخزون
 */
function createStockMovement($item_id, $movement_type, $quantity, $unit_id, $reference_type, $reference_id = null, $unit_cost = 0, $notes = '') {
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً');
    }
    
    $db = getDB();
    
    // الحصول على الرصيد الحالي
    $item = $db->selectOne("SELECT current_stock FROM items WHERE id = ?", [$item_id]);
    if (!$item) {
        throw new Exception('الصنف غير موجود');
    }
    
    $balance_before = $item['current_stock'];
    
    // حساب الرصيد الجديد
    if (in_array($movement_type, ['in', 'production'])) {
        $balance_after = $balance_before + $quantity;
    } else {
        $balance_after = $balance_before - $quantity;
        
        // التحقق من كفاية المخزون
        if ($balance_after < 0) {
            throw new Exception('المخزون غير كافي');
        }
    }
    
    // إدراج حركة المخزون
    $movement_id = $db->insert(
        "INSERT INTO stock_movements (item_id, movement_type, reference_type, reference_id, quantity, unit_id, unit_cost, total_cost, balance_before, balance_after, movement_date, notes, created_by) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE(), ?, ?)",
        [
            $item_id, $movement_type, $reference_type, $reference_id, $quantity, $unit_id,
            $unit_cost, $quantity * $unit_cost, $balance_before, $balance_after, $notes, $_SESSION['user_id']
        ]
    );
    
    // تحديث رصيد المخزون
    updateItemStock($item_id);
    
    return $movement_id;
}

/**
 * دالة لإرسال رسالة JSON
 */
function sendJsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

/**
 * دالة لإرسال رسالة نجاح
 */
function sendSuccessResponse($message, $data = null) {
    sendJsonResponse([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

/**
 * دالة لإرسال رسالة خطأ
 */
function sendErrorResponse($message, $errors = null) {
    sendJsonResponse([
        'success' => false,
        'message' => $message,
        'errors' => $errors
    ], 400);
}
?>

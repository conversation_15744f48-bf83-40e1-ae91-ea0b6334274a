<?php
/**
 * وحدة المبيعات
 * Sales Module
 */

if (!$isLoggedIn) {
    redirectTo('login');
}

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_invoice'])) {
        try {
            // بدء المعاملة
            $pdo->beginTransaction();
            
            // إنشاء رقم الفاتورة
            $stmt = $pdo->query("SELECT COUNT(*) + 1 as next_number FROM invoices WHERE invoice_type = 'sale'");
            $nextNumber = $stmt->fetch(PDO::FETCH_ASSOC)['next_number'];
            $invoiceNumber = 'INV-' . date('Y') . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
            
            // إدراج الفاتورة
            $stmt = $pdo->prepare("
                INSERT INTO invoices (
                    invoice_number, invoice_type, customer_id, invoice_date, 
                    subtotal, tax_amount, discount_amount, total_amount, 
                    notes, created_by, status
                ) VALUES (?, 'sale', ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed')
            ");
            
            $stmt->execute([
                $invoiceNumber,
                $_POST['customer_id'] ?: null,
                $_POST['invoice_date'],
                $_POST['subtotal'],
                $_POST['tax_amount'] ?: 0,
                $_POST['discount_amount'] ?: 0,
                $_POST['total_amount'],
                $_POST['notes'] ?: '',
                $currentUser['id']
            ]);
            
            $invoiceId = $pdo->lastInsertId();
            
            // إدراج تفاصيل الفاتورة
            $items = json_decode($_POST['items'], true);
            foreach ($items as $item) {
                $stmt = $pdo->prepare("
                    INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $invoiceId,
                    $item['product_id'],
                    $item['quantity'],
                    $item['unit_price'],
                    $item['total_price']
                ]);
                
                // تحديث المخزون
                $stmt = $pdo->prepare("
                    UPDATE products 
                    SET stock_quantity = stock_quantity - ? 
                    WHERE id = ?
                ");
                $stmt->execute([$item['quantity'], $item['product_id']]);
            }
            
            // إنشاء القيد المحاسبي
            $entryNumber = 'JE-' . date('Y') . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
            $stmt = $pdo->prepare("
                INSERT INTO journal_entries (
                    entry_number, entry_date, description, 
                    reference_type, reference_id, total_debit, total_credit, 
                    status, created_by
                ) VALUES (?, ?, ?, 'invoice', ?, ?, ?, 'posted', ?)
            ");
            $stmt->execute([
                $entryNumber,
                $_POST['invoice_date'],
                'قيد مبيعات - فاتورة رقم ' . $invoiceNumber,
                $invoiceId,
                $_POST['total_amount'],
                $_POST['total_amount'],
                $currentUser['id']
            ]);
            
            $entryId = $pdo->lastInsertId();
            
            // تفاصيل القيد - مدين العميل أو الصندوق
            if ($_POST['customer_id']) {
                // حساب العميل
                $accountId = 10; // حساب العملاء
            } else {
                // الصندوق الرئيسي
                $accountId = 4; // الصندوق الرئيسي
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO journal_entry_details (entry_id, account_id, debit_amount, description)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $entryId,
                $accountId,
                $_POST['total_amount'],
                'مبيعات - فاتورة رقم ' . $invoiceNumber
            ]);
            
            // دائن المبيعات
            $stmt = $pdo->prepare("
                INSERT INTO journal_entry_details (entry_id, account_id, credit_amount, description)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $entryId,
                17, // حساب مبيعات الخبز (يمكن تحسينه حسب نوع المنتج)
                $_POST['total_amount'],
                'مبيعات - فاتورة رقم ' . $invoiceNumber
            ]);
            
            $pdo->commit();
            $message = 'تم إنشاء الفاتورة بنجاح - رقم الفاتورة: ' . $invoiceNumber;
            $action = 'list';
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error = 'خطأ في إنشاء الفاتورة: ' . $e->getMessage();
        }
    }
}

// جلب البيانات حسب العملية
switch ($action) {
    case 'new':
        // جلب المنتجات والعملاء
        $products = $pdo->query("SELECT * FROM products WHERE is_active = 1 ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);
        $customers = $pdo->query("SELECT * FROM customers WHERE is_active = 1 ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);
        break;
        
    case 'list':
    default:
        // جلب قائمة الفواتير
        $stmt = $pdo->query("
            SELECT 
                i.*,
                c.name as customer_name,
                u.full_name as created_by_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            LEFT JOIN users u ON i.created_by = u.id
            WHERE i.invoice_type = 'sale'
            ORDER BY i.created_at DESC
            LIMIT 50
        ");
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        break;
}
?>

<div class="sales-module">
    <!-- رأس الوحدة -->
    <div class="module-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="fas fa-shopping-cart"></i> إدارة المبيعات</h2>
                <p class="text-muted">إدارة فواتير المبيعات والعملاء</p>
            </div>
            <div>
                <?php if ($action === 'list'): ?>
                <a href="?page=sales&action=new" class="btn btn-success">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
                <?php else: ?>
                <a href="?page=sales" class="btn btn-secondary">
                    <i class="fas fa-list"></i> قائمة الفواتير
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- الرسائل -->
    <?php if ($message): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'new'): ?>
    <!-- نموذج فاتورة جديدة -->
    <div class="card">
        <div class="card-header">
            <h4><i class="fas fa-file-invoice"></i> فاتورة مبيعات جديدة</h4>
        </div>
        <div class="card-body">
            <form method="POST" id="invoiceForm">
                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>العميل (اختياري)</label>
                            <select name="customer_id" class="form-control">
                                <option value="">مبيعات نقدية</option>
                                <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>">
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>تاريخ الفاتورة</label>
                            <input type="date" name="invoice_date" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="products-section">
                    <h5><i class="fas fa-boxes"></i> المنتجات</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="productsTable">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="productRows">
                                <tr class="product-row">
                                    <td>
                                        <select name="products[0][product_id]" class="form-control product-select" required>
                                            <option value="">اختر المنتج</option>
                                            <?php foreach ($products as $product): ?>
                                            <option value="<?php echo $product['id']; ?>" 
                                                    data-price="<?php echo $product['selling_price']; ?>"
                                                    data-stock="<?php echo $product['stock_quantity']; ?>">
                                                <?php echo htmlspecialchars($product['name']); ?> 
                                                (متوفر: <?php echo $product['stock_quantity']; ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="number" name="products[0][quantity]" class="form-control quantity-input" 
                                               min="1" step="0.001" required>
                                    </td>
                                    <td>
                                        <input type="number" name="products[0][unit_price]" class="form-control price-input" 
                                               min="0" step="0.001" required>
                                    </td>
                                    <td>
                                        <input type="number" name="products[0][total_price]" class="form-control total-input" 
                                               readonly>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-danger btn-sm remove-row">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <button type="button" class="btn btn-info" id="addProductRow">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </button>
                </div>

                <!-- الإجماليات -->
                <div class="totals-section mt-4">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="totals-box">
                                <div class="form-group">
                                    <label>المجموع الفرعي</label>
                                    <input type="number" name="subtotal" class="form-control" id="subtotal" readonly>
                                </div>
                                <div class="form-group">
                                    <label>الخصم</label>
                                    <input type="number" name="discount_amount" class="form-control" id="discount" min="0" step="0.001">
                                </div>
                                <div class="form-group">
                                    <label>الضريبة</label>
                                    <input type="number" name="tax_amount" class="form-control" id="tax" min="0" step="0.001">
                                </div>
                                <div class="form-group">
                                    <label><strong>الإجمالي النهائي</strong></label>
                                    <input type="number" name="total_amount" class="form-control" id="totalAmount" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="form-actions mt-4">
                    <input type="hidden" name="items" id="itemsData">
                    <button type="submit" name="create_invoice" class="btn btn-success">
                        <i class="fas fa-save"></i> حفظ الفاتورة
                    </button>
                    <a href="?page=sales" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <?php else: ?>
    <!-- قائمة الفواتير -->
    <div class="card">
        <div class="card-header">
            <h4><i class="fas fa-list"></i> قائمة فواتير المبيعات</h4>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>العميل</th>
                            <th>الإجمالي</th>
                            <th>الحالة</th>
                            <th>المنشئ</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoices as $invoice): ?>
                        <tr>
                            <td><strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong></td>
                            <td><?php echo formatDate($invoice['invoice_date']); ?></td>
                            <td><?php echo htmlspecialchars($invoice['customer_name'] ?: 'مبيعات نقدية'); ?></td>
                            <td><?php echo formatCurrency($invoice['total_amount']); ?></td>
                            <td>
                                <span class="badge badge-<?php echo $invoice['status'] === 'confirmed' ? 'success' : 'warning'; ?>">
                                    <?php 
                                    $statuses = [
                                        'draft' => 'مسودة',
                                        'confirmed' => 'مؤكدة',
                                        'paid' => 'مدفوعة',
                                        'cancelled' => 'ملغية'
                                    ];
                                    echo $statuses[$invoice['status']] ?? $invoice['status'];
                                    ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($invoice['created_by_name']); ?></td>
                            <td>
                                <a href="?page=sales&action=view&id=<?php echo $invoice['id']; ?>" 
                                   class="btn btn-sm btn-info" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="?page=sales&action=print&id=<?php echo $invoice['id']; ?>" 
                                   class="btn btn-sm btn-secondary" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
// JavaScript لإدارة فاتورة المبيعات
document.addEventListener('DOMContentLoaded', function() {
    let rowIndex = 1;
    
    // إضافة صف منتج جديد
    document.getElementById('addProductRow')?.addEventListener('click', function() {
        const tbody = document.getElementById('productRows');
        const newRow = createProductRow(rowIndex);
        tbody.appendChild(newRow);
        rowIndex++;
    });
    
    // إنشاء صف منتج جديد
    function createProductRow(index) {
        const row = document.createElement('tr');
        row.className = 'product-row';
        row.innerHTML = `
            <td>
                <select name="products[${index}][product_id]" class="form-control product-select" required>
                    <option value="">اختر المنتج</option>
                    <?php foreach ($products as $product): ?>
                    <option value="<?php echo $product['id']; ?>" 
                            data-price="<?php echo $product['selling_price']; ?>"
                            data-stock="<?php echo $product['stock_quantity']; ?>">
                        <?php echo htmlspecialchars($product['name']); ?> 
                        (متوفر: <?php echo $product['stock_quantity']; ?>)
                    </option>
                    <?php endforeach; ?>
                </select>
            </td>
            <td>
                <input type="number" name="products[${index}][quantity]" class="form-control quantity-input" 
                       min="1" step="0.001" required>
            </td>
            <td>
                <input type="number" name="products[${index}][unit_price]" class="form-control price-input" 
                       min="0" step="0.001" required>
            </td>
            <td>
                <input type="number" name="products[${index}][total_price]" class="form-control total-input" 
                       readonly>
            </td>
            <td>
                <button type="button" class="btn btn-danger btn-sm remove-row">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        // إضافة الأحداث للصف الجديد
        addRowEvents(row);
        return row;
    }
    
    // إضافة الأحداث لصف المنتج
    function addRowEvents(row) {
        const productSelect = row.querySelector('.product-select');
        const quantityInput = row.querySelector('.quantity-input');
        const priceInput = row.querySelector('.price-input');
        const totalInput = row.querySelector('.total-input');
        const removeBtn = row.querySelector('.remove-row');
        
        // عند اختيار المنتج
        productSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                priceInput.value = selectedOption.dataset.price;
                calculateRowTotal(row);
            }
        });
        
        // عند تغيير الكمية أو السعر
        [quantityInput, priceInput].forEach(input => {
            input.addEventListener('input', () => calculateRowTotal(row));
        });
        
        // حذف الصف
        removeBtn.addEventListener('click', function() {
            if (document.querySelectorAll('.product-row').length > 1) {
                row.remove();
                calculateTotals();
            }
        });
    }
    
    // حساب إجمالي الصف
    function calculateRowTotal(row) {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(row.querySelector('.price-input').value) || 0;
        const total = quantity * price;
        
        row.querySelector('.total-input').value = total.toFixed(3);
        calculateTotals();
    }
    
    // حساب الإجماليات
    function calculateTotals() {
        const totalInputs = document.querySelectorAll('.total-input');
        let subtotal = 0;
        
        totalInputs.forEach(input => {
            subtotal += parseFloat(input.value) || 0;
        });
        
        const discount = parseFloat(document.getElementById('discount')?.value) || 0;
        const tax = parseFloat(document.getElementById('tax')?.value) || 0;
        const total = subtotal - discount + tax;
        
        document.getElementById('subtotal').value = subtotal.toFixed(3);
        document.getElementById('totalAmount').value = total.toFixed(3);
    }
    
    // إضافة الأحداث للصفوف الموجودة
    document.querySelectorAll('.product-row').forEach(addRowEvents);
    
    // أحداث الخصم والضريبة
    ['discount', 'tax'].forEach(id => {
        document.getElementById(id)?.addEventListener('input', calculateTotals);
    });
    
    // معالجة إرسال النموذج
    document.getElementById('invoiceForm')?.addEventListener('submit', function(e) {
        const items = [];
        document.querySelectorAll('.product-row').forEach(row => {
            const productId = row.querySelector('.product-select').value;
            const quantity = row.querySelector('.quantity-input').value;
            const unitPrice = row.querySelector('.price-input').value;
            const totalPrice = row.querySelector('.total-input').value;
            
            if (productId && quantity && unitPrice) {
                items.push({
                    product_id: productId,
                    quantity: parseFloat(quantity),
                    unit_price: parseFloat(unitPrice),
                    total_price: parseFloat(totalPrice)
                });
            }
        });
        
        if (items.length === 0) {
            e.preventDefault();
            alert('يرجى إضافة منتج واحد على الأقل');
            return;
        }
        
        document.getElementById('itemsData').value = JSON.stringify(items);
    });
    
    console.log('🛒 وحدة المبيعات جاهزة');
});
</script>

<style>
.totals-box {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
}

.badge-success { background: #28a745; color: white; }
.badge-warning { background: #ffc107; color: #212529; }

.product-row:hover {
    background: rgba(102, 126, 234, 0.05);
}

.form-actions {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}
</style>

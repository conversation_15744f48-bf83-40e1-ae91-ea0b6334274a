<?php
/**
 * صفحة 404 - الصفحة غير موجودة
 * 404 Page - Page Not Found
 */
?>

<div class="error-page">
    <div class="card text-center">
        <div class="card-body">
            <!-- رقم الخطأ -->
            <div class="error-code">
                <h1 style="font-size: 6rem; color: #667eea; margin: 0; font-weight: 700;">404</h1>
            </div>
            
            <!-- رسالة الخطأ -->
            <div class="error-message">
                <h2 style="color: #333; margin: 1rem 0;">الصفحة غير موجودة</h2>
                <p style="color: #666; font-size: 1.1rem; margin-bottom: 2rem;">
                    عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
                </p>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="error-details" style="background: #f8f9fa; padding: 1.5rem; border-radius: 0.5rem; margin: 2rem 0;">
                <h5 style="color: #495057; margin-bottom: 1rem;">
                    <i class="fas fa-info-circle"></i> معلومات الطلب:
                </h5>
                <div style="text-align: right; font-size: 0.9rem; color: #6c757d;">
                    <p><strong>الصفحة المطلوبة:</strong> <?php echo htmlspecialchars($_GET['page'] ?? 'غير محدد'); ?></p>
                    <p><strong>العملية:</strong> <?php echo htmlspecialchars($_GET['action'] ?? 'غير محدد'); ?></p>
                    <p><strong>الوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                    <p><strong>المستخدم:</strong> <?php echo htmlspecialchars($currentUser['full_name'] ?? 'غير مسجل'); ?></p>
                </div>
            </div>
            
            <!-- الأزرار -->
            <div class="error-actions">
                <a href="?page=dashboard" class="btn btn-primary">
                    <i class="fas fa-home"></i> العودة للرئيسية
                </a>
                
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للخلف
                </button>
                
                <a href="?page=welcome" class="btn btn-outline">
                    <i class="fas fa-refresh"></i> إعادة تحميل
                </a>
            </div>
            
            <!-- اقتراحات -->
            <div class="suggestions" style="margin-top: 3rem;">
                <h5 style="color: #495057; margin-bottom: 1rem;">
                    <i class="fas fa-lightbulb"></i> ربما تبحث عن:
                </h5>
                
                <div class="suggestion-links" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <?php if ($isLoggedIn): ?>
                    <a href="?page=dashboard" class="suggestion-link">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </a>
                    
                    <a href="?page=sales" class="suggestion-link">
                        <i class="fas fa-shopping-cart"></i> المبيعات
                    </a>
                    
                    <a href="?page=inventory" class="suggestion-link">
                        <i class="fas fa-warehouse"></i> المخزون
                    </a>
                    
                    <a href="?page=reports" class="suggestion-link">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                    
                    <a href="?page=settings" class="suggestion-link">
                        <i class="fas fa-cogs"></i> الإعدادات
                    </a>
                    <?php else: ?>
                    <a href="?page=welcome" class="suggestion-link">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                    
                    <a href="?page=login" class="suggestion-link">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                    
                    <a href="offline-app.html" class="suggestion-link">
                        <i class="fas fa-laptop"></i> النظام المحلي
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- معلومات المساعدة -->
            <div class="help-info" style="margin-top: 3rem; padding: 1.5rem; background: #e3f2fd; border-radius: 0.5rem;">
                <h6 style="color: #1976d2; margin-bottom: 1rem;">
                    <i class="fas fa-question-circle"></i> هل تحتاج مساعدة؟
                </h6>
                <p style="color: #1976d2; font-size: 0.9rem; margin: 0;">
                    إذا كنت تواجه مشكلة مستمرة، يرجى التواصل مع مدير النظام أو فريق الدعم الفني.
                </p>
            </div>
        </div>
    </div>
</div>

<style>
/* أنماط صفحة 404 */
.error-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    padding: 2rem;
}

.error-page .card {
    max-width: 600px;
    width: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 1rem;
}

.error-code {
    position: relative;
    margin-bottom: 1rem;
}

.error-code::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 50%;
    z-index: -1;
}

.error-actions {
    margin: 2rem 0;
}

.error-actions .btn {
    margin: 0.25rem;
}

.suggestion-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    text-decoration: none;
    border-radius: 2rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.suggestion-link:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.suggestion-link i {
    font-size: 0.8rem;
}

/* تأثيرات الحركة */
.error-page .card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-code h1 {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .error-page {
        padding: 1rem;
    }
    
    .error-code h1 {
        font-size: 4rem;
    }
    
    .suggestion-links {
        flex-direction: column;
        align-items: center;
    }
    
    .suggestion-link {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
    
    .error-actions .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
}

/* تأثيرات إضافية */
.error-page .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}
</style>

<script>
// JavaScript لصفحة 404
document.addEventListener('DOMContentLoaded', function() {
    // تسجيل الخطأ في وحدة التحكم
    console.warn('🚫 صفحة 404 - الصفحة غير موجودة');
    console.log('📄 الصفحة المطلوبة:', '<?php echo htmlspecialchars($_GET['page'] ?? ''); ?>');
    console.log('⚡ العملية المطلوبة:', '<?php echo htmlspecialchars($_GET['action'] ?? ''); ?>');
    
    // إضافة تأثيرات تفاعلية
    const suggestionLinks = document.querySelectorAll('.suggestion-link');
    suggestionLinks.forEach((link, index) => {
        link.style.opacity = '0';
        link.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            link.style.transition = 'all 0.6s ease';
            link.style.opacity = '1';
            link.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // عداد تلقائي للعودة (اختياري)
    let countdown = 10;
    const countdownElement = document.createElement('p');
    countdownElement.style.cssText = 'color: #666; font-size: 0.9rem; margin-top: 1rem;';
    countdownElement.innerHTML = `سيتم توجيهك تلقائياً للرئيسية خلال <span id="countdown">${countdown}</span> ثانية`;
    
    // إضافة العداد (معطل افتراضياً)
    // document.querySelector('.error-actions').appendChild(countdownElement);
    
    // const timer = setInterval(() => {
    //     countdown--;
    //     document.getElementById('countdown').textContent = countdown;
    //     
    //     if (countdown <= 0) {
    //         clearInterval(timer);
    //         window.location.href = '?page=dashboard';
    //     }
    // }, 1000);
    
    // إضافة معالج للأزرار
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // إضافة تأثير التحميل
            if (this.href && !this.href.includes('javascript:')) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                this.disabled = true;
            }
        });
    });
    
    // تتبع النقرات للإحصائيات
    document.querySelectorAll('a').forEach(link => {
        link.addEventListener('click', function() {
            console.log('🔗 تم النقر على:', this.href || this.textContent);
        });
    });
});

// دالة للإبلاغ عن الخطأ (اختيارية)
function reportError() {
    const errorData = {
        page: '<?php echo htmlspecialchars($_GET['page'] ?? ''); ?>',
        action: '<?php echo htmlspecialchars($_GET['action'] ?? ''); ?>',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
    };
    
    console.log('📊 بيانات الخطأ:', errorData);
    
    // يمكن إرسال البيانات للخادم هنا
    // fetch('/api/report-error', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(errorData)
    // });
}

// استدعاء دالة الإبلاغ
reportError();
</script>

@echo off
chcp 65001 >nul
title تشغيل نظام إدارة المخبز المحاسبي
color 0A

echo.
echo ========================================
echo    نظام إدارة المخبز المحاسبي
echo         أنوار سوفت 2024
echo ========================================
echo.

REM التحقق من وجود ملف الترحيب
if not exist "welcome.html" (
    echo ❌ خطأ: ملف welcome.html غير موجود
    echo    يرجى التأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

echo 🚀 تشغيل نظام إدارة المخبز...
echo.

REM فتح صفحة الترحيب في المتصفح الافتراضي
echo 🌐 فتح صفحة الترحيب...
start "" "welcome.html"

echo.
echo ✅ تم فتح صفحة الترحيب في المتصفح
echo.
echo 📋 من صفحة الترحيب يمكنك:
echo    • دخول النظام الكامل (Laragon)
echo    • استخدام الوضع المحلي
echo    • فحص حالة النظام
echo.
echo 💡 نصائح:
echo    • تأكد من تشغيل Laragon للنظام الكامل
echo    • استخدم الوضع المحلي إذا لم يكن Laragon يعمل
echo    • يمكنك إغلاق هذه النافذة الآن
echo.

REM انتظار لمدة 5 ثوان ثم إغلاق تلقائي
echo ⏱️  سيتم إغلاق هذه النافذة تلقائياً خلال 5 ثوان...
timeout /t 5 >nul

echo.
echo 🎉 شكراً لاستخدام نظام إدارة المخبز!
echo 📞 للدعم الفني: أنوار سوفت
echo.

REM إغلاق تلقائي
exit

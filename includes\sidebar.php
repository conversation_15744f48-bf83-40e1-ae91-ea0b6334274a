<?php
/**
 * الشريط الجانبي المشترك
 * Shared Sidebar Component
 */

// التأكد من تحميل الدوال المطلوبة
if (!function_exists('getCurrentUser')) {
    require_once __DIR__ . '/functions.php';
}

$currentUser = getCurrentUser();
if (!$currentUser) {
    redirect('../../login.php');
}

// تحديد الصفحة الحالية لتمييز الرابط النشط
$currentPage = basename($_SERVER['PHP_SELF']);
$currentDir = basename(dirname($_SERVER['PHP_SELF']));

// دالة للتحقق من الرابط النشط
function isActiveLink($page, $dir = null) {
    global $currentPage, $currentDir;
    
    if ($dir) {
        return $currentDir === $dir ? 'active' : '';
    }
    
    return $currentPage === $page ? 'active' : '';
}

// تحديد المسار الأساسي حسب موقع الملف
$basePath = '';
if (strpos($_SERVER['PHP_SELF'], '/modules/') !== false) {
    $basePath = '../../';
}
?>

<nav class="sidebar">
    <div class="sidebar-header">
        <h3><i class="fas fa-bread-slice"></i> <?php echo APP_NAME; ?></h3>
        <p>مرحباً، <?php echo htmlspecialchars($currentUser['full_name']); ?></p>
        <small class="text-muted"><?php echo USER_ROLES[$currentUser['role']] ?? $currentUser['role']; ?></small>
    </div>
    
    <ul class="sidebar-menu">
        <!-- لوحة التحكم -->
        <li>
            <a href="<?php echo $basePath; ?>index.php" class="<?php echo isActiveLink('index.php'); ?>">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
        </li>
        
        <!-- بيانات المنشأة -->
        <?php if (hasPermission('company', 'view')): ?>
        <li>
            <a href="<?php echo $basePath; ?>modules/company/index.php" class="<?php echo isActiveLink('index.php', 'company'); ?>">
                <i class="fas fa-building"></i> بيانات المنشأة
            </a>
        </li>
        <?php endif; ?>
        
        <!-- شجرة الحسابات -->
        <?php if (hasPermission('accounts', 'view')): ?>
        <li>
            <a href="<?php echo $basePath; ?>modules/accounts/index.php" class="<?php echo isActiveLink('index.php', 'accounts'); ?>">
                <i class="fas fa-chart-line"></i> شجرة الحسابات
            </a>
        </li>
        <?php endif; ?>
        
        <!-- إدارة المستخدمين -->
        <?php if (hasPermission('users', 'view')): ?>
        <li>
            <a href="<?php echo $basePath; ?>modules/users/index.php" class="<?php echo isActiveLink('index.php', 'users'); ?>">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
        </li>
        <?php endif; ?>
        
        <!-- الصناديق والبنوك -->
        <?php if (hasPermission('cash_bank', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-money-bill-wave"></i> الصناديق والبنوك
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/cash_bank/cash_boxes.php">إدارة الصناديق</a></li>
                <li><a href="<?php echo $basePath; ?>modules/cash_bank/banks.php">إدارة البنوك</a></li>
                <li><a href="<?php echo $basePath; ?>modules/cash_bank/transfers.php">التحويلات</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- إدارة الموظفين -->
        <?php if (hasPermission('employees', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-user-tie"></i> إدارة الموظفين
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/employees/index.php">بيانات الموظفين</a></li>
                <li><a href="<?php echo $basePath; ?>modules/employees/salaries.php">الرواتب</a></li>
                <li><a href="<?php echo $basePath; ?>modules/employees/advances.php">السلف</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- إدارة المخزون -->
        <?php if (hasPermission('inventory', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-boxes"></i> إدارة المخزون
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/inventory/categories.php">فئات الأصناف</a></li>
                <li><a href="<?php echo $basePath; ?>modules/inventory/items.php">الأصناف</a></li>
                <li><a href="<?php echo $basePath; ?>modules/inventory/recipes.php">وصفات المنتجات</a></li>
                <li><a href="<?php echo $basePath; ?>modules/inventory/stock_movements.php">حركة المخزون</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- الفواتير -->
        <?php if (hasPermission('invoices', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-file-invoice"></i> الفواتير
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/invoices/sales.php">فواتير المبيعات</a></li>
                <li><a href="<?php echo $basePath; ?>modules/invoices/purchases.php">فواتير المشتريات</a></li>
                <li><a href="<?php echo $basePath; ?>modules/invoices/returns.php">المرتجعات</a></li>
                <li><a href="<?php echo $basePath; ?>modules/invoices/quick_sale.php">البيع السريع</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- السندات -->
        <?php if (hasPermission('vouchers', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-receipt"></i> السندات
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/vouchers/receipts.php">سندات القبض</a></li>
                <li><a href="<?php echo $basePath; ?>modules/vouchers/payments.php">سندات الصرف</a></li>
                <li><a href="<?php echo $basePath; ?>modules/vouchers/index.php">جميع السندات</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- الأصول الثابتة -->
        <?php if (hasPermission('assets', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-warehouse"></i> الأصول الثابتة
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/assets/index.php">إدارة الأصول</a></li>
                <li><a href="<?php echo $basePath; ?>modules/assets/depreciation.php">الإهلاك</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- التقارير -->
        <?php if (hasPermission('reports', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-chart-bar"></i> التقارير
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/reports/financial.php">التقارير المالية</a></li>
                <li><a href="<?php echo $basePath; ?>modules/reports/inventory.php">تقارير المخزون</a></li>
                <li><a href="<?php echo $basePath; ?>modules/reports/sales.php">تقارير المبيعات</a></li>
                <li><a href="<?php echo $basePath; ?>modules/reports/employees.php">تقارير الموظفين</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- الإعدادات -->
        <?php if (hasPermission('settings', 'view')): ?>
        <li class="sidebar-dropdown">
            <a href="#" class="sidebar-dropdown-toggle">
                <i class="fas fa-cog"></i> الإعدادات
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </a>
            <ul class="sidebar-submenu">
                <li><a href="<?php echo $basePath; ?>modules/settings/system.php">إعدادات النظام</a></li>
                <li><a href="<?php echo $basePath; ?>modules/settings/backup.php">النسخ الاحتياطي</a></li>
                <li><a href="<?php echo $basePath; ?>modules/settings/logs.php">سجل النشاطات</a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- تسجيل الخروج -->
        <li>
            <a href="<?php echo $basePath; ?>logout.php" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </li>
    </ul>
</nav>

<style>
/* تنسيق إضافي للشريط الجانبي */
.sidebar-dropdown {
    position: relative;
}

.sidebar-dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-icon {
    transition: transform 0.3s ease;
}

.sidebar-dropdown.open .dropdown-icon {
    transform: rotate(180deg);
}

.sidebar-submenu {
    display: none;
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: rgba(0, 0, 0, 0.1);
}

.sidebar-dropdown.open .sidebar-submenu {
    display: block;
}

.sidebar-submenu li {
    border-bottom: none;
}

.sidebar-submenu a {
    padding: 0.5rem 1rem 0.5rem 2rem;
    font-size: 0.9rem;
}

.sidebar-submenu a:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* تمييز الرابط النشط */
.sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-right: 3px solid #fff;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
}
</style>

<script>
// JavaScript للقوائم المنسدلة
document.addEventListener('DOMContentLoaded', function() {
    const dropdownToggles = document.querySelectorAll('.sidebar-dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            
            const dropdown = this.closest('.sidebar-dropdown');
            const isOpen = dropdown.classList.contains('open');
            
            // إغلاق جميع القوائم المنسدلة الأخرى
            document.querySelectorAll('.sidebar-dropdown').forEach(item => {
                item.classList.remove('open');
            });
            
            // فتح/إغلاق القائمة الحالية
            if (!isOpen) {
                dropdown.classList.add('open');
            }
        });
    });
    
    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.sidebar-dropdown')) {
            document.querySelectorAll('.sidebar-dropdown').forEach(item => {
                item.classList.remove('open');
            });
        }
    });
});
</script>

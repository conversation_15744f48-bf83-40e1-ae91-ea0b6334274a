<?php
/**
 * صفحة الترحيب الرئيسية
 * Main Welcome Page
 */
?>

<div class="welcome-screen">
    <div class="card">
        <!-- الشعار والعنوان -->
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="font-size: 4rem; color: #667eea; margin-bottom: 1rem;">
                <i class="fas fa-bread-slice"></i>
            </div>
            <h1 style="background: linear-gradient(135deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                مرحباً بك في نظام إدارة المخبز
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-top: 1rem;">
                النظام المحاسبي المتكامل لإدارة جميع عمليات المخبز<br>
                محاسبة • مخزون • مبيعات • موظفين • تقارير
            </p>
        </div>

        <!-- حالة النظام -->
        <div style="text-align: center; margin-bottom: 2rem;">
            <?php if ($dbStatus === 'connected'): ?>
                <div class="status-indicator status-connected" style="font-size: 1.1rem; padding: 1rem 2rem;">
                    <i class="fas fa-check-circle"></i>
                    النظام جاهز للعمل - قاعدة البيانات متصلة
                </div>
            <?php elseif ($dbStatus === 'setup_needed'): ?>
                <div class="status-indicator status-setup" style="font-size: 1.1rem; padding: 1rem 2rem;">
                    <i class="fas fa-cog"></i>
                    النظام يحتاج إعداد قاعدة البيانات
                </div>
            <?php else: ?>
                <div class="status-indicator status-error" style="font-size: 1.1rem; padding: 1rem 2rem;">
                    <i class="fas fa-exclamation-triangle"></i>
                    خطأ في الاتصال بقاعدة البيانات
                </div>
            <?php endif; ?>
        </div>

        <!-- الأزرار الرئيسية -->
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 2rem;">
            <?php if ($dbStatus === 'connected'): ?>
                <?php if (!$isLoggedIn): ?>
                    <a href="?page=login" class="btn btn-success" style="min-width: 200px;">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </a>
                <?php else: ?>
                    <a href="?page=dashboard" class="btn btn-success" style="min-width: 200px;">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                <?php endif; ?>
                
                <a href="offline-app.html" class="btn" style="min-width: 200px;">
                    <i class="fas fa-laptop"></i>
                    الوضع المحلي
                </a>
                
            <?php elseif ($dbStatus === 'setup_needed'): ?>
                <a href="?page=setup" class="btn btn-warning" style="min-width: 200px;">
                    <i class="fas fa-rocket"></i>
                    إعداد النظام
                </a>
                
                <a href="offline-app.html" class="btn" style="min-width: 200px;">
                    <i class="fas fa-laptop"></i>
                    استخدام الوضع المحلي
                </a>
                
            <?php else: ?>
                <a href="?page=setup" class="btn btn-danger" style="min-width: 200px;">
                    <i class="fas fa-tools"></i>
                    إصلاح قاعدة البيانات
                </a>
                
                <a href="offline-app.html" class="btn" style="min-width: 200px;">
                    <i class="fas fa-laptop"></i>
                    الوضع المحلي
                </a>
            <?php endif; ?>
        </div>

        <!-- معلومات النظام -->
        <div style="background: #f8f9fa; border-radius: 1rem; padding: 2rem; margin-top: 2rem;">
            <h3 style="color: #333; margin-bottom: 1rem; text-align: center;">
                <i class="fas fa-star"></i> مميزات النظام
            </h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>محاسبة متكاملة ودقيقة</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>إدارة المخزون والمنتجات</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>نظام الفواتير والمبيعات</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>إدارة الموظفين والرواتب</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>التقارير المالية الشاملة</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>يعمل أوفلاين وأونلاين</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>أمان عالي وحماية البيانات</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem;">
                    <i class="fas fa-check-circle" style="color: #28a745; font-size: 1.2rem;"></i>
                    <span>واجهة سهلة ومتجاوبة</span>
                </div>
            </div>
        </div>

        <!-- معلومات تقنية -->
        <div style="background: #e3f2fd; border-radius: 1rem; padding: 1.5rem; margin-top: 2rem; text-align: center;">
            <h4 style="color: #1976d2; margin-bottom: 1rem;">
                <i class="fas fa-info-circle"></i> معلومات النظام
            </h4>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; font-size: 0.9rem;">
                <div>
                    <strong>الإصدار:</strong> <?php echo APP_VERSION; ?>
                </div>
                <div>
                    <strong>المطور:</strong> <?php echo APP_AUTHOR; ?>
                </div>
                <div>
                    <strong>النوع:</strong> نظام هجين HTML+PHP
                </div>
                <div>
                    <strong>قاعدة البيانات:</strong> MySQL
                </div>
                <div>
                    <strong>الخادم:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?>
                </div>
                <div>
                    <strong>PHP:</strong> <?php echo phpversion(); ?>
                </div>
            </div>
        </div>

        <?php if ($dbStatus === 'connected' && !$isLoggedIn): ?>
        <!-- معلومات تسجيل الدخول -->
        <div style="background: #fff3cd; border-radius: 1rem; padding: 1.5rem; margin-top: 2rem; text-align: center;">
            <h4 style="color: #856404; margin-bottom: 1rem;">
                <i class="fas fa-key"></i> بيانات تسجيل الدخول التجريبية
            </h4>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div>
                    <strong>المدير:</strong> admin / admin123
                </div>
                <div>
                    <strong>المحاسب:</strong> accountant / admin123
                </div>
                <div>
                    <strong>الكاشير:</strong> cashier1 / admin123
                </div>
                <div>
                    <strong>المدير:</strong> manager / admin123
                </div>
            </div>
            
            <p style="font-size: 0.9rem; color: #856404; margin-top: 1rem;">
                <i class="fas fa-exclamation-triangle"></i>
                يرجى تغيير كلمات المرور بعد تسجيل الدخول
            </p>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// تأثيرات بصرية للصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير تدرجي للظهور
    const card = document.querySelector('.welcome-screen .card');
    if (card) {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);
    }
    
    // تأثير النقر على الأزرار
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // إضافة تأثير الموجة
            const ripple = document.createElement('span');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255,255,255,0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.left = (e.clientX - e.target.offsetLeft) + 'px';
            ripple.style.top = (e.clientY - e.target.offsetTop) + 'px';
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    console.log('🏠 صفحة الترحيب جاهزة');
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .welcome-screen .card {
        animation: slideInUp 0.8s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
</script>

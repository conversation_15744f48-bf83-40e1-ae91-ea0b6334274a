<?php
/**
 * ملف التحقق من صحة البيانات
 * Data Validation Functions
 */

/**
 * كلاس التحقق من صحة البيانات
 */
class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
    }
    
    /**
     * التحقق من وجود الحقل
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field] = $message ?: "حقل {$field} مطلوب";
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأدنى لطول النص
     */
    public function minLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && mb_strlen(trim($this->data[$field])) < $length) {
            $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون على الأقل {$length} أحرف";
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأقصى لطول النص
     */
    public function maxLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && mb_strlen(trim($this->data[$field])) > $length) {
            $this->errors[$field] = $message ?: "حقل {$field} يجب أن لا يزيد عن {$length} أحرف";
        }
        return $this;
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون بريد إلكتروني صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من أن القيمة رقم
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!is_numeric($this->data[$field])) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون رقم";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من أن القيمة رقم صحيح
     */
    public function integer($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_INT)) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون رقم صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأدنى للقيمة
     */
    public function min($field, $min, $message = null) {
        if (isset($this->data[$field]) && is_numeric($this->data[$field])) {
            if ($this->data[$field] < $min) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون على الأقل {$min}";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من الحد الأقصى للقيمة
     */
    public function max($field, $max, $message = null) {
        if (isset($this->data[$field]) && is_numeric($this->data[$field])) {
            if ($this->data[$field] > $max) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن لا يزيد عن {$max}";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة التاريخ
     */
    public function date($field, $format = 'Y-m-d', $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $date = DateTime::createFromFormat($format, $this->data[$field]);
            if (!$date || $date->format($format) !== $this->data[$field]) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون تاريخ صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من تطابق القيم
     */
    public function match($field, $matchField, $message = null) {
        if (isset($this->data[$field]) && isset($this->data[$matchField])) {
            if ($this->data[$field] !== $this->data[$matchField]) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يطابق {$matchField}";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من وجود القيمة في قائمة
     */
    public function in($field, $values, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!in_array($this->data[$field], $values)) {
                $this->errors[$field] = $message ?: "حقل {$field} يحتوي على قيمة غير صحيحة";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من عدم وجود القيمة في قاعدة البيانات (فريد)
     */
    public function unique($field, $table, $column = null, $excludeId = null, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $column = $column ?: $field;
            $db = getDB();
            
            $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = ?";
            $params = [$this->data[$field]];
            
            if ($excludeId) {
                $query .= " AND id != ?";
                $params[] = $excludeId;
            }
            
            $result = $db->selectOne($query, $params);
            if ($result['count'] > 0) {
                $this->errors[$field] = $message ?: "حقل {$field} موجود مسبقاً";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من وجود القيمة في قاعدة البيانات
     */
    public function exists($field, $table, $column = null, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $column = $column ?: 'id';
            $db = getDB();
            
            $result = $db->selectOne(
                "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = ?",
                [$this->data[$field]]
            );
            
            if ($result['count'] == 0) {
                $this->errors[$field] = $message ?: "حقل {$field} غير موجود";
            }
        }
        return $this;
    }
    
    /**
     * التحقق المخصص
     */
    public function custom($field, $callback, $message = null) {
        if (isset($this->data[$field])) {
            if (!$callback($this->data[$field])) {
                $this->errors[$field] = $message ?: "حقل {$field} غير صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     */
    public function phone($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $phone = preg_replace('/[^0-9+]/', '', $this->data[$field]);
            if (strlen($phone) < 7 || strlen($phone) > 15) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون رقم هاتف صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة كلمة المرور
     */
    public function password($field, $minLength = 6, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $password = $this->data[$field];
            if (strlen($password) < $minLength) {
                $this->errors[$field] = $message ?: "كلمة المرور يجب أن تكون على الأقل {$minLength} أحرف";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public function strongPassword($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (class_exists('Security')) {
                $errors = Security::validatePasswordStrength($this->data[$field]);
                if (!empty($errors)) {
                    $this->errors[$field] = $message ?: implode(', ', $errors);
                }
            } else {
                // تحقق بسيط إذا لم تكن فئة الأمان متاحة
                $password = $this->data[$field];
                $errors = [];
                
                if (strlen($password) < 8) {
                    $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                }
                
                if (!preg_match('/[A-Z]/', $password)) {
                    $errors[] = 'يجب أن تحتوي على حرف كبير واحد على الأقل';
                }
                
                if (!preg_match('/[a-z]/', $password)) {
                    $errors[] = 'يجب أن تحتوي على حرف صغير واحد على الأقل';
                }
                
                if (!preg_match('/[0-9]/', $password)) {
                    $errors[] = 'يجب أن تحتوي على رقم واحد على الأقل';
                }
                
                if (!preg_match('/[^A-Za-z0-9]/', $password)) {
                    $errors[] = 'يجب أن تحتوي على رمز خاص واحد على الأقل';
                }
                
                if (!empty($errors)) {
                    $this->errors[$field] = $message ?: implode(', ', $errors);
                }
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة URL
     */
    public function url($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_URL)) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون رابط صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة IP
     */
    public function ip($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_IP)) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون عنوان IP صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة JSON
     */
    public function json($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            json_decode($this->data[$field]);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون JSON صحيح";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من النمط (Regex)
     */
    public function pattern($field, $pattern, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!preg_match($pattern, $this->data[$field])) {
                $this->errors[$field] = $message ?: "حقل {$field} لا يطابق النمط المطلوب";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من أن القيمة منطقية (boolean)
     */
    public function boolean($field, $message = null) {
        if (isset($this->data[$field])) {
            $value = $this->data[$field];
            if (!is_bool($value) && !in_array($value, [0, 1, '0', '1', 'true', 'false', 'on', 'off', 'yes', 'no'])) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون قيمة منطقية";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من أن القيمة مصفوفة
     */
    public function array($field, $message = null) {
        if (isset($this->data[$field]) && !is_array($this->data[$field])) {
            $this->errors[$field] = $message ?: "حقل {$field} يجب أن يكون مصفوفة";
        }
        return $this;
    }
    
    /**
     * التحقق من عدد عناصر المصفوفة
     */
    public function arraySize($field, $min = null, $max = null, $message = null) {
        if (isset($this->data[$field]) && is_array($this->data[$field])) {
            $count = count($this->data[$field]);
            
            if ($min !== null && $count < $min) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن يحتوي على {$min} عناصر على الأقل";
            }
            
            if ($max !== null && $count > $max) {
                $this->errors[$field] = $message ?: "حقل {$field} يجب أن لا يزيد عن {$max} عناصر";
            }
        }
        return $this;
    }
    
    /**
     * التحقق من صحة الملف المرفوع
     */
    public function file($field, $allowedTypes = [], $maxSize = null, $message = null) {
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] !== UPLOAD_ERR_NO_FILE) {
            $file = $_FILES[$field];
            
            // التحقق من وجود خطأ في الرفع
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $this->errors[$field] = $message ?: "خطأ في رفع الملف";
                return $this;
            }
            
            // التحقق من نوع الملف
            if (!empty($allowedTypes)) {
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                if (!in_array($extension, $allowedTypes)) {
                    $this->errors[$field] = $message ?: "نوع الملف غير مسموح";
                    return $this;
                }
            }
            
            // التحقق من حجم الملف
            $maxSize = $maxSize ?: MAX_FILE_SIZE;
            if ($file['size'] > $maxSize) {
                $maxSizeMB = round($maxSize / (1024 * 1024), 2);
                $this->errors[$field] = $message ?: "حجم الملف يجب أن لا يزيد عن {$maxSizeMB} ميجابايت";
            }
        }
        return $this;
    }
    
    /**
     * الحصول على الأخطاء
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * التحقق من وجود أخطاء
     */
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    /**
     * الحصول على أول خطأ
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : null;
    }
    
    /**
     * إضافة خطأ مخصص
     */
    public function addError($field, $message) {
        $this->errors[$field] = $message;
        return $this;
    }
    
    /**
     * مسح الأخطاء
     */
    public function clearErrors() {
        $this->errors = [];
        return $this;
    }
}

/**
 * دالة مساعدة لإنشاء مدقق جديد
 */
function validator($data = []) {
    return new Validator($data);
}

/**
 * دالة للتحقق من صحة البيانات المحاسبية
 */
function validateAccountingEntry($entries) {
    $errors = [];
    $totalDebit = 0;
    $totalCredit = 0;
    
    if (empty($entries)) {
        $errors[] = 'يجب إدخال بنود القيد';
        return $errors;
    }
    
    foreach ($entries as $index => $entry) {
        if (empty($entry['account_id'])) {
            $errors[] = "يجب اختيار الحساب في البند " . ($index + 1);
        }
        
        $debit = floatval($entry['debit_amount'] ?? 0);
        $credit = floatval($entry['credit_amount'] ?? 0);
        
        if ($debit == 0 && $credit == 0) {
            $errors[] = "يجب إدخال مبلغ في البند " . ($index + 1);
        }
        
        if ($debit > 0 && $credit > 0) {
            $errors[] = "لا يمكن إدخال مبلغ مدين ودائن في نفس البند " . ($index + 1);
        }
        
        $totalDebit += $debit;
        $totalCredit += $credit;
    }
    
    // التحقق من التوازن
    if (abs($totalDebit - $totalCredit) > 0.01) {
        $errors[] = 'القيد غير متوازن - إجمالي المدين يجب أن يساوي إجمالي الدائن';
    }
    
    return $errors;
}
?>

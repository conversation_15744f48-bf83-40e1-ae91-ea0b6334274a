<?php
/**
 * إعدادات الأمان المتقدمة
 * Advanced Security Configuration
 */

// منع الوصول المباشر
if (!defined('APP_PATH')) {
    die('Access denied');
}

/**
 * فئة الأمان
 */
class Security {
    
    /**
     * توليد رمز CSRF
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * التحقق من رمز CSRF
     */
    public static function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * تشفير كلمة المرور
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * تنظيف البيانات المدخلة
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        // إزالة المسافات الزائدة
        $input = trim($input);
        
        // تحويل الأحرف الخاصة
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        // إزالة العلامات الضارة
        $input = strip_tags($input);
        
        return $input;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePasswordStrength($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        return $errors;
    }
    
    /**
     * تسجيل محاولات تسجيل الدخول الفاشلة
     */
    public static function logFailedLogin($username, $ip) {
        $key = "failed_login_{$ip}";
        $attempts = $_SESSION[$key] ?? 0;
        $_SESSION[$key] = $attempts + 1;
        $_SESSION["{$key}_time"] = time();
        
        // تسجيل في ملف السجل
        error_log("Failed login attempt: Username: {$username}, IP: {$ip}, Attempts: " . ($_SESSION[$key]));
    }
    
    /**
     * التحقق من حظر IP
     */
    public static function isIPBlocked($ip) {
        $key = "failed_login_{$ip}";
        $attempts = $_SESSION[$key] ?? 0;
        $lastAttempt = $_SESSION["{$key}_time"] ?? 0;
        
        // إذا تجاوز عدد المحاولات الحد المسموح
        if ($attempts >= MAX_LOGIN_ATTEMPTS) {
            // التحقق من انتهاء فترة الحظر
            if (time() - $lastAttempt < LOGIN_TIMEOUT) {
                return true;
            } else {
                // إعادة تعيين العداد بعد انتهاء فترة الحظر
                unset($_SESSION[$key], $_SESSION["{$key}_time"]);
            }
        }
        
        return false;
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول الفاشلة
     */
    public static function resetFailedLogins($ip) {
        $key = "failed_login_{$ip}";
        unset($_SESSION[$key], $_SESSION["{$key}_time"]);
    }
    
    /**
     * توليد رمز تذكر آمن
     */
    public static function generateRememberToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * تشفير البيانات الحساسة
     */
    public static function encrypt($data, $key = null) {
        if ($key === null) {
            $key = hash('sha256', APP_SECRET_KEY ?? 'default_secret_key');
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    public static function decrypt($encryptedData, $key = null) {
        if ($key === null) {
            $key = hash('sha256', APP_SECRET_KEY ?? 'default_secret_key');
        }
        
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    public static function validateSession() {
        // التحقق من انتهاء صلاحية الجلسة
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > SESSION_TIMEOUT) {
            session_destroy();
            return false;
        }
        
        // التحقق من تغيير IP (اختياري)
        if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== ($_SERVER['REMOTE_ADDR'] ?? '')) {
            session_destroy();
            return false;
        }
        
        // تجديد معرف الجلسة دورياً
        if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 300) {
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
        
        return true;
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public static function cleanupExpiredSessions() {
        // يمكن تنفيذ هذا كمهمة مجدولة
        // لحذف الجلسات المنتهية الصلاحية من قاعدة البيانات
    }
    
    /**
     * إنشاء رأس أمان HTTP
     */
    public static function setSecurityHeaders() {
        // منع تضمين الصفحة في إطار
        header('X-Frame-Options: DENY');
        
        // منع تخمين نوع المحتوى
        header('X-Content-Type-Options: nosniff');
        
        // تفعيل حماية XSS
        header('X-XSS-Protection: 1; mode=block');
        
        // سياسة أمان المحتوى
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com;");
        
        // إجبار HTTPS (في بيئة الإنتاج)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
        
        // منع تخزين الصفحة في الكاش
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Pragma: no-cache');
        header('Expires: 0');
    }
}

// تعيين رؤوس الأمان
Security::setSecurityHeaders();

// إضافة مفتاح سري للتطبيق (يجب تغييره في بيئة الإنتاج)
if (!defined('APP_SECRET_KEY')) {
    define('APP_SECRET_KEY', 'your-secret-key-change-this-in-production');
}
?>
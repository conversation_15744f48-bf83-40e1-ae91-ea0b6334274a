<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المخبز - الوضع المحلي</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="نظام المخبز">
    
    <!-- CSS Framework -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    
    <style>
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --success: #28a745;
            --danger: #dc3545;
            --warning: #ffc107;
            --info: #17a2b8;
            --light: #f8f9fa;
            --dark: #343a40;
            --white: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', sans-serif;
            background: var(--light);
            direction: rtl;
            overflow-x: hidden;
        }

        /* الشريط الجانبي */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            width: 280px;
            background: linear-gradient(180deg, var(--dark), #495057);
            color: var(--white);
            overflow-y: auto;
            z-index: 1000;
            transform: translateX(0);
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 1.5rem 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .sidebar-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu a {
            display: block;
            padding: 1rem;
            color: var(--white);
            text-decoration: none;
            transition: all 0.3s;
            border-right: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-right-color: var(--primary);
            transform: translateX(-5px);
        }

        .sidebar-menu i {
            width: 20px;
            margin-left: 10px;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 280px;
            padding: 1rem;
            min-height: 100vh;
            transition: margin-right 0.3s ease;
        }

        /* الهيدر */
        .header {
            background: var(--white);
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 1.5rem;
            color: var(--dark);
        }

        /* البطاقات */
        .card {
            background: var(--white);
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-header {
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            border-bottom: 1px solid #dee2e6;
        }

        .card-body {
            padding: 1rem;
        }

        /* نظام الشبكة */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -15px;
        }

        .col-3 { flex: 0 0 25%; max-width: 25%; padding: 0 15px; }
        .col-6 { flex: 0 0 50%; max-width: 50%; padding: 0 15px; }
        .col-12 { flex: 0 0 100%; max-width: 100%; padding: 0 15px; }

        /* بطاقات الإحصائيات */
        .stats-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        /* الجداول */
        .table {
            width: 100%;
            margin-bottom: 1rem;
            color: #212529;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            vertical-align: top;
            border-top: 1px solid #dee2e6;
            text-align: right;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 600;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* الأزرار */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            margin: 0.25rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
        }

        .btn-success { background: var(--success); color: var(--white); }
        .btn-warning { background: var(--warning); color: var(--dark); }
        .btn-info { background: var(--info); color: var(--white); }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* الأدوات المساعدة */
        .text-center { text-align: center; }
        .text-success { color: var(--success); }
        .text-warning { color: var(--warning); }
        .text-muted { color: #6c757d; }
        .d-flex { display: flex; }
        .justify-content-between { justify-content: space-between; }
        .align-items-center { align-items: center; }
        .mb-4 { margin-bottom: 1.5rem; }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 0.5rem;
            }
            
            .col-3, .col-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
            
            .stats-number {
                font-size: 1.5rem;
            }
        }

        /* شريط الحالة */
        .status-bar {
            background: var(--warning);
            color: var(--dark);
            padding: 0.5rem 1rem;
            text-align: center;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1001;
        }

        .main-content {
            margin-top: 50px;
        }

        /* تأثيرات الحركة */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            animation: fadeInUp 0.6s ease;
        }
    </style>
</head>
<body>
    <!-- شريط الحالة -->
    <div class="status-bar">
        <i class="fas fa-laptop"></i> الوضع المحلي - البيانات محفوظة في المتصفح
    </div>

    <!-- الشريط الجانبي -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-bread-slice"></i> نظام المخبز</h3>
            <p>مرحباً، مدير النظام</p>
        </div>
        
        <ul class="sidebar-menu">
            <li><a href="#" onclick="showDashboard()" class="active">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a></li>
            <li><a href="#" onclick="showCompanySettings()">
                <i class="fas fa-building"></i> بيانات المنشأة
            </a></li>
            <li><a href="#" onclick="showAccounts()">
                <i class="fas fa-chart-line"></i> شجرة الحسابات
            </a></li>
            <li><a href="#" onclick="showUsers()">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a></li>
            <li><a href="#" onclick="showCashBank()">
                <i class="fas fa-money-bill-wave"></i> الصناديق والبنوك
            </a></li>
            <li><a href="#" onclick="showEmployees()">
                <i class="fas fa-user-tie"></i> إدارة الموظفين
            </a></li>
            <li><a href="#" onclick="showInventory()">
                <i class="fas fa-boxes"></i> إدارة المخزون
            </a></li>
            <li><a href="#" onclick="showInvoices()">
                <i class="fas fa-file-invoice"></i> الفواتير
            </a></li>
            <li><a href="#" onclick="showVouchers()">
                <i class="fas fa-receipt"></i> السندات
            </a></li>
            <li><a href="#" onclick="showReports()">
                <i class="fas fa-chart-bar"></i> التقارير
            </a></li>
            <li><a href="#" onclick="exportData()">
                <i class="fas fa-download"></i> تصدير البيانات
            </a></li>
            <li><a href="#" onclick="goBack()">
                <i class="fas fa-arrow-right"></i> العودة للرئيسية
            </a></li>
        </ul>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div id="app-content">
            <!-- سيتم تحميل المحتوى هنا -->
        </div>
    </main>

    <!-- JavaScript للنظام المحلي -->
    <script>
        // بيانات النظام المحلية
        const LOCAL_DATA = {
            company: {
                name: 'مخبز أنوار',
                nameEn: 'Anwar Bakery',
                address: 'صنعاء - اليمن',
                phone: '01-123456',
                mobile: '*********',
                email: '<EMAIL>',
                currency: 'ر.ي'
            },
            accounts: [
                { id: 1, code: '1', name: 'الأصول', type: 'asset', balance: 0 },
                { id: 2, code: '2', name: 'الخصوم', type: 'liability', balance: 0 },
                { id: 3, code: '3', name: 'حقوق الملكية', type: 'equity', balance: 0 },
                { id: 4, code: '4', name: 'الإيرادات', type: 'revenue', balance: 0 },
                { id: 5, code: '5', name: 'المصروفات', type: 'expense', balance: 0 }
            ],
            cashBoxes: [
                { id: 1, name: 'الصندوق الرئيسي', code: 'CASH001', balance: 15000, responsible: 'أحمد محمد' },
                { id: 2, name: 'صندوق المبيعات', code: 'CASH002', balance: 10000, responsible: 'فاطمة علي' }
            ],
            banks: [
                { id: 1, name: 'البنك الأهلي اليمني', accountNumber: '*********', accountName: 'مخبز أنوار', balance: 25000 }
            ],
            employees: [
                { id: 1, code: 'EMP001', name: 'أحمد محمد علي', jobTitle: 'أمين صندوق', salary: 80000, department: 'المبيعات', isActive: true },
                { id: 2, code: 'EMP002', name: 'فاطمة علي حسن', jobTitle: 'محاسبة', salary: 90000, department: 'المحاسبة', isActive: true },
                { id: 3, code: 'EMP003', name: 'محمد سالم', jobTitle: 'خباز رئيسي', salary: 85000, department: 'الإنتاج', isActive: true }
            ],
            items: [
                { id: 1, name: 'دقيق أبيض', code: 'ITEM001', category: 'خامات الخبز', unit: 'كيس', currentStock: 100, minStock: 20, cost: 1500, price: 0 },
                { id: 2, name: 'خبز عربي', code: 'ITEM002', category: 'منتجات جاهزة', unit: 'رغيف', currentStock: 500, minStock: 50, cost: 25, price: 50 },
                { id: 3, name: 'أكياس بلاستيك', code: 'ITEM003', category: 'مواد تعبئة', unit: 'قطعة', currentStock: 1000, minStock: 100, cost: 2, price: 0 }
            ],
            stats: {
                todaySales: 45000,
                todayPurchases: 15000,
                totalCash: 25000,
                totalBank: 25000,
                totalEmployees: 3,
                activeEmployees: 3
            }
        };

        // حفظ البيانات في LocalStorage
        function saveData() {
            localStorage.setItem('bakerySystemData', JSON.stringify(LOCAL_DATA));
        }

        // تحميل البيانات من LocalStorage
        function loadData() {
            const saved = localStorage.getItem('bakerySystemData');
            if (saved) {
                Object.assign(LOCAL_DATA, JSON.parse(saved));
            }
        }

        // عرض لوحة التحكم
        function showDashboard() {
            const content = document.getElementById('app-content');
            content.innerHTML = `
                <!-- الهيدر -->
                <div class="header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                        <div>
                            <span id="currentDateTime">${formatDateTime(new Date())}</span>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-3">
                        <div class="stats-card">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <h6>مبيعات اليوم</h6>
                            <div class="stats-number">${formatMoney(LOCAL_DATA.stats.todaySales)}</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stats-card">
                            <i class="fas fa-shopping-bag fa-2x mb-2"></i>
                            <h6>مشتريات اليوم</h6>
                            <div class="stats-number">${formatMoney(LOCAL_DATA.stats.todayPurchases)}</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stats-card">
                            <i class="fas fa-cash-register fa-2x mb-2"></i>
                            <h6>إجمالي السيولة</h6>
                            <div class="stats-number">${formatMoney(LOCAL_DATA.stats.totalCash + LOCAL_DATA.stats.totalBank)}</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stats-card">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h6>عدد الموظفين</h6>
                            <div class="stats-number">${LOCAL_DATA.stats.activeEmployees}</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- أرصدة الصناديق -->
                    <div class="col-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-money-bill-wave"></i> أرصدة الصناديق</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم الصندوق</th>
                                            <th>المسؤول</th>
                                            <th>الرصيد</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${LOCAL_DATA.cashBoxes.map(box => `
                                            <tr>
                                                <td>${box.name}</td>
                                                <td>${box.responsible}</td>
                                                <td class="text-success">${formatMoney(box.balance)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- الموظفين النشطين -->
                    <div class="col-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-user-tie"></i> الموظفين النشطين</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم الموظف</th>
                                            <th>المسمى الوظيفي</th>
                                            <th>الراتب</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${LOCAL_DATA.employees.map(emp => `
                                            <tr>
                                                <td>${emp.name}</td>
                                                <td>${emp.jobTitle}</td>
                                                <td class="text-success">${formatMoney(emp.salary)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأصناف التي تحتاج إعادة طلب -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exclamation-triangle text-warning"></i> أصناف تحتاج إعادة طلب</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم الصنف</th>
                                            <th>المخزون الحالي</th>
                                            <th>الحد الأدنى</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${LOCAL_DATA.items.filter(item => item.currentStock <= item.minStock).map(item => `
                                            <tr>
                                                <td>${item.name}</td>
                                                <td>${item.currentStock} ${item.unit}</td>
                                                <td>${item.minStock} ${item.unit}</td>
                                                <td><span class="text-warning">يحتاج إعادة طلب</span></td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // تحديث الوقت كل ثانية
            setInterval(() => {
                const dateTimeElement = document.getElementById('currentDateTime');
                if (dateTimeElement) {
                    dateTimeElement.textContent = formatDateTime(new Date());
                }
            }, 1000);

            // تحديث القائمة النشطة
            updateActiveMenu('showDashboard()');
        }

        // دوال الوحدات الأخرى
        function showCompanySettings() {
            alert('وحدة بيانات المنشأة - قيد التطوير في الوضع المحلي');
        }

        function showAccounts() {
            alert('وحدة شجرة الحسابات - قيد التطوير في الوضع المحلي');
        }

        function showUsers() {
            alert('وحدة إدارة المستخدمين - قيد التطوير في الوضع المحلي');
        }

        function showCashBank() {
            alert('وحدة الصناديق والبنوك - قيد التطوير في الوضع المحلي');
        }

        function showEmployees() {
            alert('وحدة إدارة الموظفين - قيد التطوير في الوضع المحلي');
        }

        function showInventory() {
            alert('وحدة إدارة المخزون - قيد التطوير في الوضع المحلي');
        }

        function showInvoices() {
            alert('وحدة الفواتير - قيد التطوير في الوضع المحلي');
        }

        function showVouchers() {
            alert('وحدة السندات - قيد التطوير في الوضع المحلي');
        }

        function showReports() {
            alert('وحدة التقارير - قيد التطوير في الوضع المحلي');
        }

        function exportData() {
            const dataStr = JSON.stringify(LOCAL_DATA, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'bakery-data-' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
            URL.revokeObjectURL(url);
        }

        function goBack() {
            window.location.href = 'laragon-app.php';
        }

        // دوال مساعدة
        function formatMoney(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount) + ' ر.ي';
        }

        function formatDateTime(date) {
            return new Intl.DateTimeFormat('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).format(date);
        }

        function updateActiveMenu(functionName) {
            document.querySelectorAll('.sidebar-menu a').forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('onclick') === functionName) {
                    link.classList.add('active');
                }
            });
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            showDashboard();
            
            // حفظ البيانات كل 30 ثانية
            setInterval(saveData, 30000);
            
            console.log('🍞 نظام إدارة المخبز - الوضع المحلي');
            console.log('💾 البيانات محفوظة في:', 'LocalStorage');
        });

        // حفظ البيانات عند إغلاق الصفحة
        window.addEventListener('beforeunload', saveData);
    </script>
</body>
</html>

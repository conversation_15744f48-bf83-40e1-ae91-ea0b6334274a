<?php
/**
 * وحدة إدارة الصناديق والبنوك
 * Cash Boxes and Banks Management Module
 */

// تضمين الملفات المطلوبة
require_once '../../config/database.php';
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/validation.php';

// التحقق من الصلاحيات
requirePermission('cash_bank', 'view');

$db = getDB();
$error = '';
$success = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add_cash_box':
            if (!hasPermission('cash_bank', 'add')) {
                $error = 'ليس لديك صلاحية لإضافة صندوق جديد';
                break;
            }

            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('box_name', 'اسم الصندوق مطلوب')
                     ->required('box_code', 'رمز الصندوق مطلوب')
                     ->unique('box_code', 'cash_boxes', 'box_code', null, 'رمز الصندوق موجود مسبقاً')
                     ->numeric('opening_balance', 'الرصيد الافتتاحي يجب أن يكون رقم');

            if (!$validator->hasErrors()) {
                try {
                    $db->beginTransaction();

                    // إنشاء حساب للصندوق في شجرة الحسابات
                    $cash_account_parent = $db->selectOne(
                        "SELECT id FROM chart_of_accounts WHERE account_code = '1111' LIMIT 1"
                    );

                    if (!$cash_account_parent) {
                        throw new Exception('لم يتم العثور على حساب الصناديق الرئيسي');
                    }

                    // توليد رمز حساب فرعي
                    $last_account = $db->selectOne(
                        "SELECT account_code FROM chart_of_accounts
                         WHERE parent_id = ? AND account_code LIKE '1111%'
                         ORDER BY account_code DESC LIMIT 1",
                        [$cash_account_parent['id']]
                    );

                    $next_code = $last_account ?
                        (intval(substr($last_account['account_code'], 4)) + 1) : 1;
                    $account_code = '1111' . str_pad($next_code, 2, '0', STR_PAD_LEFT);

                    // إنشاء الحساب
                    $account_id = $db->insert(
                        "INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, opening_balance, current_balance, created_by)
                         VALUES (?, ?, ?, 'asset', 5, ?, ?, ?)",
                        [
                            $account_code,
                            'صندوق ' . cleanInput($_POST['box_name']),
                            $cash_account_parent['id'],
                            floatval($_POST['opening_balance'] ?? 0),
                            floatval($_POST['opening_balance'] ?? 0),
                            $_SESSION['user_id']
                        ]
                    );

                    // إنشاء الصندوق
                    $box_id = $db->insert(
                        "INSERT INTO cash_boxes (box_name, box_code, account_id, responsible_user_id, opening_balance, current_balance, description, created_by)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                        [
                            cleanInput($_POST['box_name']),
                            cleanInput($_POST['box_code']),
                            $account_id,
                            !empty($_POST['responsible_user_id']) ? intval($_POST['responsible_user_id']) : null,
                            floatval($_POST['opening_balance'] ?? 0),
                            floatval($_POST['opening_balance'] ?? 0),
                            cleanInput($_POST['description']),
                            $_SESSION['user_id']
                        ]
                    );

                    // إنشاء قيد افتتاحي إذا كان هناك رصيد
                    $opening_balance = floatval($_POST['opening_balance'] ?? 0);
                    if ($opening_balance != 0) {
                        $capital_account = $db->selectOne(
                            "SELECT id FROM chart_of_accounts WHERE account_type = 'equity' ORDER BY id LIMIT 1"
                        );

                        if ($capital_account) {
                            $entries = [
                                [
                                    'account_id' => $account_id,
                                    'debit_amount' => $opening_balance,
                                    'credit_amount' => 0,
                                    'description' => 'رصيد افتتاحي للصندوق'
                                ],
                                [
                                    'account_id' => $capital_account['id'],
                                    'debit_amount' => 0,
                                    'credit_amount' => $opening_balance,
                                    'description' => 'مقابل رصيد افتتاحي للصندوق'
                                ]
                            ];

                            createJournalEntry('قيد افتتاحي - ' . $_POST['box_name'], $entries, 'opening', $box_id);
                        }
                    }

                    $db->commit();

                    logUserActivity('create', 'cash_boxes', $box_id, null, $_POST);
                    $success = 'تم إضافة الصندوق بنجاح';

                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'خطأ في إضافة الصندوق: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;

        case 'add_bank':
            if (!hasPermission('cash_bank', 'add')) {
                $error = 'ليس لديك صلاحية لإضافة حساب بنكي جديد';
                break;
            }

            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('bank_name', 'اسم البنك مطلوب')
                     ->required('account_number', 'رقم الحساب مطلوب')
                     ->required('account_name', 'اسم الحساب مطلوب')
                     ->unique('account_number', 'banks', 'account_number', null, 'رقم الحساب موجود مسبقاً')
                     ->numeric('opening_balance', 'الرصيد الافتتاحي يجب أن يكون رقم');

            if (!$validator->hasErrors()) {
                try {
                    $db->beginTransaction();

                    // إنشاء حساب للبنك في شجرة الحسابات
                    $bank_account_parent = $db->selectOne(
                        "SELECT id FROM chart_of_accounts WHERE account_code = '1112' LIMIT 1"
                    );

                    if (!$bank_account_parent) {
                        throw new Exception('لم يتم العثور على حساب البنوك الرئيسي');
                    }

                    // توليد رمز حساب فرعي
                    $last_account = $db->selectOne(
                        "SELECT account_code FROM chart_of_accounts
                         WHERE parent_id = ? AND account_code LIKE '1112%'
                         ORDER BY account_code DESC LIMIT 1",
                        [$bank_account_parent['id']]
                    );

                    $next_code = $last_account ?
                        (intval(substr($last_account['account_code'], 4)) + 1) : 1;
                    $account_code = '1112' . str_pad($next_code, 2, '0', STR_PAD_LEFT);

                    // إنشاء الحساب
                    $account_id = $db->insert(
                        "INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, opening_balance, current_balance, created_by)
                         VALUES (?, ?, ?, 'asset', 5, ?, ?, ?)",
                        [
                            $account_code,
                            $_POST['bank_name'] . ' - ' . $_POST['account_name'],
                            $bank_account_parent['id'],
                            floatval($_POST['opening_balance'] ?? 0),
                            floatval($_POST['opening_balance'] ?? 0),
                            $_SESSION['user_id']
                        ]
                    );

                    // إنشاء الحساب البنكي
                    $bank_id = $db->insert(
                        "INSERT INTO banks (bank_name, account_number, account_name, account_id, branch, iban, swift_code, opening_balance, current_balance, description, created_by)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        [
                            cleanInput($_POST['bank_name']),
                            cleanInput($_POST['account_number']),
                            cleanInput($_POST['account_name']),
                            $account_id,
                            cleanInput($_POST['branch']),
                            cleanInput($_POST['iban']),
                            cleanInput($_POST['swift_code']),
                            floatval($_POST['opening_balance'] ?? 0),
                            floatval($_POST['opening_balance'] ?? 0),
                            cleanInput($_POST['description']),
                            $_SESSION['user_id']
                        ]
                    );

                    // إنشاء قيد افتتاحي إذا كان هناك رصيد
                    $opening_balance = floatval($_POST['opening_balance'] ?? 0);
                    if ($opening_balance != 0) {
                        $capital_account = $db->selectOne(
                            "SELECT id FROM chart_of_accounts WHERE account_type = 'equity' ORDER BY id LIMIT 1"
                        );

                        if ($capital_account) {
                            $entries = [
                                [
                                    'account_id' => $account_id,
                                    'debit_amount' => $opening_balance,
                                    'credit_amount' => 0,
                                    'description' => 'رصيد افتتاحي للحساب البنكي'
                                ],
                                [
                                    'account_id' => $capital_account['id'],
                                    'debit_amount' => 0,
                                    'credit_amount' => $opening_balance,
                                    'description' => 'مقابل رصيد افتتاحي للحساب البنكي'
                                ]
                            ];

                            createJournalEntry('قيد افتتاحي - ' . $_POST['bank_name'], $entries, 'opening', $bank_id);
                        }
                    }

                    $db->commit();

                    logUserActivity('create', 'banks', $bank_id, null, $_POST);
                    $success = 'تم إضافة الحساب البنكي بنجاح';

                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'خطأ في إضافة الحساب البنكي: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;
    }
}

// جلب البيانات
try {
    // جلب الصناديق
    $cash_boxes = $db->select(
        "SELECT cb.*, u.full_name as responsible_name, a.account_code
         FROM cash_boxes cb
         LEFT JOIN users u ON cb.responsible_user_id = u.id
         LEFT JOIN chart_of_accounts a ON cb.account_id = a.id
         WHERE cb.is_active = 1
         ORDER BY cb.box_name"
    );

    // جلب البنوك
    $banks = $db->select(
        "SELECT b.*, a.account_code
         FROM banks b
         LEFT JOIN chart_of_accounts a ON b.account_id = a.id
         WHERE b.is_active = 1
         ORDER BY b.bank_name"
    );

    // جلب المستخدمين للقائمة المنسدلة
    $users = $db->select(
        "SELECT id, full_name FROM users WHERE is_active = 1 ORDER BY full_name"
    );

    // حساب الإحصائيات
    $total_cash = array_sum(array_column($cash_boxes, 'current_balance'));
    $total_bank = array_sum(array_column($banks, 'current_balance'));

} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
}

$pageTitle = 'إدارة الصناديق والبنوك';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="../../assets/css/style.css">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .balance-amount {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .cash-box-card, .bank-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .cash-box-card:hover, .bank-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .account-balance {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .balance-positive { color: #28a745; }
        .balance-negative { color: #dc3545; }
        .balance-zero { color: #6c757d; }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .tab-buttons {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-button {
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الهيدر -->
        <header class="header">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-money-bill-wave"></i> <?php echo $pageTitle; ?></h1>
                <div>
                    <?php if (hasPermission('cash_bank', 'add')): ?>
                    <button type="button" class="btn btn-primary" onclick="openAddModal()">
                        <i class="fas fa-plus"></i> إضافة جديد
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </header>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="balance-card">
                    <i class="fas fa-cash-register fa-2x mb-2"></i>
                    <h5>إجمالي أرصدة الصناديق</h5>
                    <div class="balance-amount"><?php echo formatMoney($total_cash ?? 0); ?></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="balance-card">
                    <i class="fas fa-university fa-2x mb-2"></i>
                    <h5>إجمالي أرصدة البنوك</h5>
                    <div class="balance-amount"><?php echo formatMoney($total_bank ?? 0); ?></div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="balance-card">
                    <i class="fas fa-wallet fa-2x mb-2"></i>
                    <h5>إجمالي السيولة</h5>
                    <div class="balance-amount"><?php echo formatMoney(($total_cash ?? 0) + ($total_bank ?? 0)); ?></div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الصناديق -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cash-register"></i> الصناديق</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($cash_boxes)): ?>
                            <?php foreach ($cash_boxes as $box): ?>
                            <div class="cash-box-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($box['box_name']); ?></h6>
                                        <small class="text-muted">
                                            رمز: <?php echo htmlspecialchars($box['box_code']); ?> |
                                            حساب: <?php echo htmlspecialchars($box['account_code']); ?>
                                        </small>
                                        <?php if ($box['responsible_name']): ?>
                                        <br><small class="text-info">
                                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($box['responsible_name']); ?>
                                        </small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <?php
                                        $balance = $box['current_balance'];
                                        $class = $balance > 0 ? 'balance-positive' : ($balance < 0 ? 'balance-negative' : 'balance-zero');
                                        ?>
                                        <div class="account-balance <?php echo $class; ?>">
                                            <?php echo formatMoney(abs($balance)); ?>
                                        </div>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-info" onclick="viewCashBoxDetails(<?php echo $box['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="openTransferModal(<?php echo $box['id']; ?>, 'cash')">
                                                <i class="fas fa-exchange-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-cash-register fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد صناديق مسجلة</h6>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- البنوك -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-university"></i> الحسابات البنكية</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($banks)): ?>
                            <?php foreach ($banks as $bank): ?>
                            <div class="bank-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($bank['bank_name']); ?></h6>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($bank['account_name']); ?><br>
                                            رقم الحساب: <?php echo htmlspecialchars($bank['account_number']); ?>
                                        </small>
                                        <?php if ($bank['branch']): ?>
                                        <br><small class="text-info">
                                            <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($bank['branch']); ?>
                                        </small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <?php
                                        $balance = $bank['current_balance'];
                                        $class = $balance > 0 ? 'balance-positive' : ($balance < 0 ? 'balance-negative' : 'balance-zero');
                                        ?>
                                        <div class="account-balance <?php echo $class; ?>">
                                            <?php echo formatMoney(abs($balance)); ?>
                                        </div>
                                        <div class="mt-2">
                                            <button class="btn btn-sm btn-info" onclick="viewBankDetails(<?php echo $bank['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="openTransferModal(<?php echo $bank['id']; ?>, 'bank')">
                                                <i class="fas fa-exchange-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-university fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد حسابات بنكية مسجلة</h6>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة جديد -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addModal')">&times;</span>
            <h3><i class="fas fa-plus"></i> إضافة جديد</h3>

            <!-- أزرار التبويب -->
            <div class="tab-buttons">
                <button type="button" class="tab-button active" onclick="switchTab('cash_box')">
                    <i class="fas fa-cash-register"></i> صندوق جديد
                </button>
                <button type="button" class="tab-button" onclick="switchTab('bank')">
                    <i class="fas fa-university"></i> حساب بنكي جديد
                </button>
            </div>

            <!-- تبويب إضافة صندوق -->
            <div id="cash_box_tab" class="tab-content active">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add_cash_box">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="box_name" class="form-label">اسم الصندوق *</label>
                                <input type="text" name="box_name" id="box_name" class="form-control" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="box_code" class="form-label">رمز الصندوق *</label>
                                <input type="text" name="box_code" id="box_code" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="responsible_user_id" class="form-label">المستخدم المسؤول</label>
                                <select name="responsible_user_id" id="responsible_user_id" class="form-select">
                                    <option value="">اختر المستخدم</option>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>"><?php echo htmlspecialchars($user['full_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="opening_balance" class="form-label">الرصيد الافتتاحي</label>
                                <input type="number" name="opening_balance" id="opening_balance" class="form-control" step="0.01" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">وصف الصندوق</label>
                        <textarea name="description" id="description" class="form-control" rows="3"></textarea>
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الصندوق
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addModal')">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>

            <!-- تبويب إضافة حساب بنكي -->
            <div id="bank_tab" class="tab-content">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add_bank">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bank_name" class="form-label">اسم البنك *</label>
                                <input type="text" name="bank_name" id="bank_name" class="form-control" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="branch" class="form-label">الفرع</label>
                                <input type="text" name="branch" id="branch" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account_number" class="form-label">رقم الحساب *</label>
                                <input type="text" name="account_number" id="account_number" class="form-control" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account_name" class="form-label">اسم الحساب *</label>
                                <input type="text" name="account_name" id="account_name" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="iban" class="form-label">رقم الآيبان</label>
                                <input type="text" name="iban" id="iban" class="form-control">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="swift_code" class="form-label">رمز السويفت</label>
                                <input type="text" name="swift_code" id="swift_code" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="bank_opening_balance" class="form-label">الرصيد الافتتاحي</label>
                        <input type="number" name="opening_balance" id="bank_opening_balance" class="form-control" step="0.01" value="0">
                    </div>

                    <div class="form-group">
                        <label for="bank_description" class="form-label">وصف الحساب</label>
                        <textarea name="description" id="bank_description" class="form-control" rows="3"></textarea>
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الحساب البنكي
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addModal')">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function switchTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة التفعيل من جميع الأزرار
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // تفعيل التبويب المحدد
            document.getElementById(tabName + '_tab').classList.add('active');
            event.target.classList.add('active');
        }

        function viewCashBoxDetails(boxId) {
            window.open(`cash_box_details.php?id=${boxId}`, '_blank');
        }

        function viewBankDetails(bankId) {
            window.open(`bank_details.php?id=${bankId}`, '_blank');
        }

        function openTransferModal(accountId, type) {
            window.open(`transfer.php?from_id=${accountId}&from_type=${type}`, '_blank');
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // توليد رمز تلقائي للصندوق
        document.getElementById('box_name').addEventListener('input', function() {
            const name = this.value;
            if (name) {
                const code = 'CASH' + String(Date.now()).slice(-4);
                document.getElementById('box_code').value = code;
            }
        });

        // تحديث الصفحة كل 30 ثانية لتحديث الأرصدة
        setInterval(function() {
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>

<?php
/**
 * معالج إعداد قاعدة البيانات المتكامل
 * Complete Database Setup Handler
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'bakery_system');

$setupSteps = [];
$errors = [];
$warnings = [];
$stats = [];

try {
    // الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $setupSteps[] = '✅ تم الاتصال بـ MySQL بنجاح';
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        $warnings[] = 'قاعدة البيانات موجودة مسبقاً - سيتم التحديث';
    }
    
    // تنفيذ ملف إنشاء قاعدة البيانات
    if (file_exists('create-database.sql')) {
        $sql = file_get_contents('create-database.sql');
        
        // تقسيم الاستعلامات
        $queries = array_filter(array_map('trim', explode(';', $sql)));
        
        $executedQueries = 0;
        foreach ($queries as $query) {
            if (!empty($query) && !preg_match('/^--/', $query)) {
                try {
                    $pdo->exec($query);
                    $executedQueries++;
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة
                    if (strpos($e->getMessage(), 'already exists') === false && 
                        strpos($e->getMessage(), 'Duplicate entry') === false) {
                        $warnings[] = 'تحذير: ' . $e->getMessage();
                    }
                }
            }
        }
        
        $setupSteps[] = "✅ تم تنفيذ ملف إنشاء قاعدة البيانات ($executedQueries استعلام)";
    } else {
        $errors[] = 'ملف create-database.sql غير موجود';
    }
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $setupSteps[] = '✅ تم الاتصال بقاعدة البيانات: ' . DB_NAME;
    
    // فحص الجداول المطلوبة
    $requiredTables = [
        'company_settings', 'users', 'chart_of_accounts', 'cash_boxes', 'banks',
        'employees', 'product_categories', 'products', 'customers', 'suppliers',
        'invoices', 'invoice_items', 'journal_entries', 'journal_entry_details',
        'payments', 'fixed_assets', 'system_settings', 'activity_log'
    ];
    
    $existingTables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existingTables[] = $row[0];
    }
    
    $missingTables = array_diff($requiredTables, $existingTables);
    
    if (empty($missingTables)) {
        $setupSteps[] = '✅ جميع الجداول المطلوبة موجودة (' . count($existingTables) . ' جدول)';
    } else {
        $errors[] = 'جداول مفقودة: ' . implode(', ', $missingTables);
    }
    
    // تنفيذ البيانات التجريبية
    if (file_exists('sample-data.sql') && empty($missingTables)) {
        // فحص إذا كانت البيانات موجودة
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        
        if ($userCount == 0) {
            $sql = file_get_contents('sample-data.sql');
            $queries = array_filter(array_map('trim', explode(';', $sql)));
            
            $insertedCount = 0;
            foreach ($queries as $query) {
                if (!empty($query) && !preg_match('/^--/', $query)) {
                    try {
                        $pdo->exec($query);
                        if (stripos($query, 'INSERT') === 0) {
                            $insertedCount++;
                        }
                    } catch (PDOException $e) {
                        if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                            $warnings[] = 'تحذير في البيانات: ' . $e->getMessage();
                        }
                    }
                }
            }
            
            $setupSteps[] = "✅ تم إدراج البيانات التجريبية ($insertedCount عملية إدراج)";
        } else {
            $setupSteps[] = "⚠️ البيانات موجودة مسبقاً ($userCount مستخدم)";
        }
    }
    
    // إحصائيات قاعدة البيانات
    foreach ($requiredTables as $table) {
        if (in_array($table, $existingTables)) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $stmt->fetchColumn();
                $stats[$table] = $count;
            } catch (PDOException $e) {
                $stats[$table] = 'خطأ';
            }
        }
    }
    
    $setupSteps[] = '📊 إحصائيات قاعدة البيانات جاهزة';
    
    // إنشاء مستخدم افتراضي إذا لم يكن موجوداً
    if (isset($stats['users']) && $stats['users'] == 0) {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role, permissions) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            'admin',
            $hashedPassword,
            'مدير النظام',
            '<EMAIL>',
            'admin',
            json_encode(['all'])
        ]);
        $setupSteps[] = '✅ تم إنشاء المستخدم الافتراضي (admin/admin123)';
    }
    
    if (empty($errors)) {
        $setupSteps[] = '🎉 تم إعداد قاعدة البيانات بنجاح!';
    }
    
} catch (PDOException $e) {
    $errors[] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
} catch (Exception $e) {
    $errors[] = 'خطأ عام: ' . $e->getMessage();
}

$success = empty($errors);

// معلومات الاتصال
$connectionInfo = [
    'Host' => DB_HOST,
    'Database' => DB_NAME,
    'User' => DB_USER,
    'Status' => $success ? 'متصل' : 'خطأ'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - نظام إدارة المخبز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: bold;
            margin-top: 1rem;
        }

        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; color: #212529; }
        .status-error { background: #dc3545; }

        .content {
            padding: 2rem;
        }

        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-right-color: #28a745;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-right-color: #ffc107;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-right-color: #dc3545;
        }

        .steps-list {
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .step-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .step-item:last-child {
            border-bottom: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }

        .connection-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.25rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-database"></i> إعداد قاعدة البيانات</h1>
            <p>إعداد قاعدة بيانات نظام إدارة المخبز المحاسبي</p>
            <div class="status-badge status-<?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $success ? 'نجح الإعداد' : 'فشل الإعداد'; ?>
            </div>
        </div>
        
        <div class="content">
            <!-- معلومات الاتصال -->
            <div class="connection-info">
                <h4><i class="fas fa-info-circle"></i> معلومات الاتصال</h4>
                <?php foreach ($connectionInfo as $key => $value): ?>
                <div class="info-item">
                    <span><?php echo $key; ?>:</span>
                    <span><?php echo $value; ?></span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- النتائج -->
            <?php if ($success): ?>
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle"></i> تم إعداد قاعدة البيانات بنجاح!</h4>
                <p>جميع الجداول والبيانات التجريبية جاهزة للاستخدام.</p>
            </div>
            <?php endif; ?>

            <!-- الأخطاء -->
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-circle"></i> أخطاء:</h4>
                <ul>
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- التحذيرات -->
            <?php if (!empty($warnings)): ?>
            <div class="alert alert-warning">
                <h4><i class="fas fa-exclamation-triangle"></i> تحذيرات:</h4>
                <ul>
                    <?php foreach ($warnings as $warning): ?>
                    <li><?php echo htmlspecialchars($warning); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- خطوات الإعداد -->
            <div class="steps-list">
                <h4><i class="fas fa-list-check"></i> خطوات الإعداد:</h4>
                <?php foreach ($setupSteps as $step): ?>
                <div class="step-item"><?php echo $step; ?></div>
                <?php endforeach; ?>
            </div>

            <!-- إحصائيات الجداول -->
            <?php if (!empty($stats)): ?>
            <h4><i class="fas fa-chart-bar"></i> إحصائيات الجداول:</h4>
            <div class="stats-grid">
                <?php foreach ($stats as $table => $count): ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $count; ?></div>
                    <div><?php echo $table; ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- أزرار العمل -->
            <div style="text-align: center; margin-top: 2rem;">
                <?php if ($success): ?>
                <a href="laragon-app.php" class="btn btn-success">
                    <i class="fas fa-rocket"></i> دخول النظام
                </a>
                
                <a href="database-monitor.php" class="btn">
                    <i class="fas fa-database"></i> مراقب قاعدة البيانات
                </a>
                <?php else: ?>
                <button onclick="location.reload()" class="btn btn-warning">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
                <?php endif; ?>
                
                <a href="welcome.html" class="btn">
                    <i class="fas fa-home"></i> العودة للرئيسية
                </a>
            </div>

            <!-- معلومات تسجيل الدخول -->
            <?php if ($success && isset($stats['users']) && $stats['users'] > 0): ?>
            <div style="margin-top: 2rem; padding: 1rem; background: #d1ecf1; border-radius: 0.5rem; text-align: center;">
                <h4>🔑 بيانات تسجيل الدخول الافتراضية:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
                <p style="font-size: 0.9rem; color: #0c5460;">يرجى تغيير كلمة المرور بعد تسجيل الدخول</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        console.log('🗄️ إعداد قاعدة البيانات');
        console.log('✅ الحالة:', <?php echo $success ? 'true' : 'false'; ?>);
        
        <?php if ($success): ?>
        // إعادة توجيه تلقائية بعد 5 ثوان
        setTimeout(() => {
            if (confirm('هل تريد الانتقال إلى النظام الآن؟')) {
                window.location.href = 'laragon-app.php';
            }
        }, 5000);
        <?php endif; ?>
    </script>
</body>
</html>

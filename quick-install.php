<?php
/**
 * تثبيت سريع للنظام في Laragon
 * Quick Installation for Laragon
 */

// إعدادات التثبيت
$laragonPath = 'C:\\laragon\\www\\';
$projectName = 'bakery-system';
$projectPath = $laragonPath . $projectName . '\\';

$installSteps = [];
$errors = [];

// فحص وجود Laragon
if (!is_dir($laragonPath)) {
    $errors[] = 'لم يتم العثور على Laragon في المسار: ' . $laragonPath;
} else {
    $installSteps[] = '✅ تم العثور على Laragon';
}

// إنشاء مجلد المشروع
if (empty($errors)) {
    if (!is_dir($projectPath)) {
        if (mkdir($projectPath, 0755, true)) {
            $installSteps[] = '✅ تم إنشاء مجلد المشروع: ' . $projectPath;
        } else {
            $errors[] = 'فشل في إنشاء مجلد المشروع: ' . $projectPath;
        }
    } else {
        $installSteps[] = '⚠️ مجلد المشروع موجود مسبقاً: ' . $projectPath;
    }
}

// نسخ الملفات
if (empty($errors)) {
    $files = [
        'laragon-app.php' => 'الملف الرئيسي',
        'setup-database.php' => 'معالج الإعداد',
        'offline-app.html' => 'النسخة المحلية',
        'check-system.php' => 'فحص النظام'
    ];
    
    foreach ($files as $file => $description) {
        if (file_exists($file)) {
            if (copy($file, $projectPath . $file)) {
                $installSteps[] = "✅ تم نسخ: $file ($description)";
            } else {
                $errors[] = "فشل في نسخ: $file";
            }
        } else {
            // إنشاء الملف إذا لم يكن موجوداً
            $installSteps[] = "⚠️ ملف غير موجود: $file - سيتم إنشاؤه";
        }
    }
    
    // إنشاء ملف index.php للتوجيه
    $indexContent = '<?php header("Location: laragon-app.php"); exit(); ?>';
    if (file_put_contents($projectPath . 'index.php', $indexContent)) {
        $installSteps[] = '✅ تم إنشاء ملف التوجيه: index.php';
    }
}

// إنشاء الملفات المفقودة
if (empty($errors)) {
    // إنشاء laragon-app.php إذا لم يكن موجوداً
    if (!file_exists($projectPath . 'laragon-app.php')) {
        $laragonAppContent = '<?php
/**
 * النظام المحاسبي الهجين للمخابز - محسن لـ Laragon
 */

// إعدادات النظام
define("APP_NAME", "نظام إدارة المخبز المحاسبي");
define("APP_VERSION", "2.0.0");
define("DB_HOST", "localhost");
define("DB_NAME", "bakery_system");
define("DB_USER", "root");
define("DB_PASS", "");

// كشف البيئة
$isLaragon = isset($_SERVER["HTTP_HOST"]) && (strpos($_SERVER["HTTP_HOST"], ".test") !== false || $_SERVER["HTTP_HOST"] === "localhost");
$dbAvailable = false;
$tablesExist = false;

// فحص قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbAvailable = true;
    
    $stmt = $pdo->query("SHOW TABLES LIKE \"users\"");
    $tablesExist = $stmt->rowCount() > 0;
    
} catch (PDOException $e) {
    $dbAvailable = false;
}

// تحديد وضع العمل
if ($isLaragon && $dbAvailable && $tablesExist) {
    $workMode = "online";
} elseif ($isLaragon && $dbAvailable && !$tablesExist) {
    $workMode = "setup";
} else {
    $workMode = "offline";
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - النسخة الهجينة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea, #764ba2); margin: 0; padding: 2rem; min-height: 100vh; direction: rtl; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 1rem; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 2rem; text-align: center; }
        .content { padding: 2rem; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; text-decoration: none; border-radius: 0.5rem; margin: 0.5rem; }
        .status-badge { background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.85rem; }
        .alert { padding: 1rem; margin: 1rem 0; border-radius: 0.5rem; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bread-slice"></i> مرحباً بك في نظام إدارة المخبز</h1>
            <p>النظام المحاسبي الهجين المتكامل - محسن لـ Laragon</p>
            <span class="status-badge">v<?php echo APP_VERSION; ?></span>
        </div>
        
        <div class="content">
            <?php if ($workMode === "setup"): ?>
                <div class="alert alert-info">
                    <h3><i class="fas fa-tools"></i> إعداد النظام لأول مرة</h3>
                    <p>تم اكتشاف Laragon بنجاح! يحتاج النظام لإنشاء قاعدة البيانات والجداول.</p>
                </div>
                <div style="text-align: center;">
                    <a href="setup-database.php" class="btn">
                        <i class="fas fa-rocket"></i> بدء الإعداد التلقائي
                    </a>
                    <a href="offline-app.html" class="btn">
                        <i class="fas fa-laptop"></i> العمل في الوضع المحلي
                    </a>
                </div>
                
            <?php elseif ($workMode === "online"): ?>
                <div class="alert alert-success">
                    <h3><i class="fas fa-check-circle"></i> النظام جاهز للعمل</h3>
                    <p><strong>الخادم:</strong> Laragon (<?php echo $_SERVER["HTTP_HOST"]; ?>)</p>
                    <p><strong>قاعدة البيانات:</strong> MySQL متصلة</p>
                    <p><strong>وضع العمل:</strong> هجين (أونلاين + أوفلاين)</p>
                </div>
                <div style="text-align: center;">
                    <a href="dashboard.php" class="btn">
                        <i class="fas fa-tachometer-alt"></i> دخول لوحة التحكم
                    </a>
                    <a href="login.php" class="btn">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
                
            <?php else: ?>
                <div class="alert alert-warning">
                    <h3><i class="fas fa-laptop"></i> الوضع المحلي</h3>
                    <p>النظام يعمل محلياً باستخدام تقنيات المتصفح المتقدمة.</p>
                </div>
                <div style="text-align: center;">
                    <a href="offline-app.html" class="btn">
                        <i class="fas fa-play"></i> بدء النظام المحلي
                    </a>
                    <a href="check-system.php" class="btn">
                        <i class="fas fa-stethoscope"></i> فحص النظام
                    </a>
                </div>
            <?php endif; ?>
            
            <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 0.5rem; text-align: center;">
                <h4>المميزات:</h4>
                <p>✅ محاسبة متكاملة | ✅ إدارة المخزون | ✅ الفواتير والمبيعات | ✅ إدارة الموظفين</p>
                <p>✅ يعمل على كل الأجهزة | ✅ أوفلاين وأونلاين | ✅ أمان عالي</p>
            </div>
        </div>
    </div>
</body>
</html>';
        
        if (file_put_contents($projectPath . 'laragon-app.php', $laragonAppContent)) {
            $installSteps[] = '✅ تم إنشاء: laragon-app.php';
        }
    }
}

$success = empty($errors);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت سريع - نظام إدارة المخبز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .step {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid #28a745;
            background: #f8f9fa;
        }

        .error {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            text-align: center;
            border: 1px solid #c3e6cb;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .path-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-download"></i> تثبيت سريع</h1>
            <p>تثبيت نظام إدارة المخبز في Laragon</p>
        </div>
        
        <div class="content">
            <?php if ($success): ?>
                <div class="success-message">
                    <h3><i class="fas fa-check-circle"></i> تم التثبيت بنجاح!</h3>
                    <p>تم نسخ جميع الملفات إلى Laragon بنجاح.</p>
                    
                    <div class="path-info">
                        <strong>مسار المشروع:</strong><br>
                        <?php echo $projectPath; ?>
                    </div>
                    
                    <a href="http://localhost/<?php echo $projectName; ?>/" class="btn" target="_blank">
                        <i class="fas fa-external-link-alt"></i> فتح النظام
                    </a>
                    
                    <a href="http://localhost/<?php echo $projectName; ?>/check-system.php" class="btn" target="_blank">
                        <i class="fas fa-stethoscope"></i> فحص النظام
                    </a>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <h3><i class="fas fa-exclamation-triangle"></i> الأخطاء:</h3>
                <?php foreach ($errors as $error): ?>
                    <div class="error"><?php echo $error; ?></div>
                <?php endforeach; ?>
                
                <div style="margin-top: 2rem; padding: 1rem; background: #fff3cd; border-radius: 0.5rem;">
                    <h4>الحلول المقترحة:</h4>
                    <ul>
                        <li>تأكد من تثبيت Laragon في المسار الافتراضي</li>
                        <li>تأكد من تشغيل Laragon كمدير</li>
                        <li>تحقق من صلاحيات الكتابة في مجلد www</li>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($installSteps)): ?>
                <h3><i class="fas fa-list-check"></i> خطوات التثبيت:</h3>
                <?php foreach ($installSteps as $step): ?>
                    <div class="step"><?php echo $step; ?></div>
                <?php endforeach; ?>
            <?php endif; ?>

            <div style="margin-top: 2rem; text-align: center;">
                <h4>الخطوات التالية:</h4>
                <ol style="text-align: right; display: inline-block;">
                    <li>تأكد من تشغيل Laragon (Apache + MySQL)</li>
                    <li>افتح النظام من الرابط أعلاه</li>
                    <li>اتبع تعليمات الإعداد إذا ظهرت</li>
                    <li>ابدأ استخدام النظام!</li>
                </ol>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
    <script>
        // فتح النظام تلقائياً بعد 3 ثوان
        setTimeout(() => {
            window.open('http://localhost/<?php echo $projectName; ?>/', '_blank');
        }, 3000);
    </script>
    <?php endif; ?>
</body>
</html>

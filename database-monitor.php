<?php
/**
 * مراقب قاعدة البيانات
 * Database Monitor
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakery_system');
define('DB_USER', 'root');
define('DB_PASS', '');

$dbInfo = [];
$errors = [];

try {
    // الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معلومات الخادم
    $version = $pdo->query("SELECT VERSION()")->fetchColumn();
    $dbInfo['MySQL Version'] = $version;
    
    // مسار البيانات
    $dataDir = $pdo->query("SELECT @@datadir")->fetchColumn();
    $dbInfo['Data Directory'] = $dataDir;
    
    // حجم قاعدة البيانات
    $stmt = $pdo->prepare("
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = ?
    ");
    $stmt->execute([DB_NAME]);
    $size = $stmt->fetchColumn();
    $dbInfo['Database Size'] = $size ? $size . ' MB' : 'غير محسوب';
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
    if ($stmt->rowCount() > 0) {
        $dbInfo['Database Status'] = '✅ موجودة';
        
        // الاتصال بقاعدة البيانات
        $pdo->exec("USE " . DB_NAME);
        
        // عدد الجداول
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $dbInfo['Tables Count'] = count($tables);
        
        // تفاصيل الجداول
        $dbInfo['Tables'] = [];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("
                SELECT 
                    ROUND((data_length + index_length) / 1024, 2) AS size_kb
                FROM information_schema.tables 
                WHERE table_schema = ? AND table_name = ?
            ");
            $stmt->execute([DB_NAME, $table]);
            $tableSize = $stmt->fetchColumn();
            
            $dbInfo['Tables'][$table] = [
                'records' => $count,
                'size' => $tableSize ? $tableSize . ' KB' : '< 1 KB'
            ];
        }
        
        // آخر نشاط
        $stmt = $pdo->query("
            SELECT 
                table_name,
                update_time
            FROM information_schema.tables 
            WHERE table_schema = '" . DB_NAME . "' 
            AND update_time IS NOT NULL
            ORDER BY update_time DESC 
            LIMIT 5
        ");
        $dbInfo['Recent Activity'] = $stmt->fetchAll();
        
    } else {
        $dbInfo['Database Status'] = '❌ غير موجودة';
    }
    
} catch (PDOException $e) {
    $errors[] = 'خطأ في الاتصال: ' . $e->getMessage();
}

// معلومات النظام
$systemInfo = [
    'PHP Version' => phpversion(),
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'غير محدد',
    'Current Time' => date('Y-m-d H:i:s'),
    'Timezone' => date_default_timezone_get()
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقب قاعدة البيانات - نظام إدارة المخبز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }

        .card-body {
            padding: 1rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-right-color: #dc3545;
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }

        .path-display {
            background: #e3f2fd;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.9rem;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-database"></i> مراقب قاعدة البيانات</h1>
            <p>مراقبة حالة وأداء قاعدة بيانات نظام إدارة المخبز</p>
            <div style="margin-top: 1rem;">
                <span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.9rem;">
                    <i class="fas fa-clock"></i> آخر تحديث: <?php echo date('H:i:s'); ?>
                </span>
            </div>
        </div>
        
        <div class="content">
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-circle"></i> أخطاء:</h4>
                <ul>
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <div class="grid">
                <!-- معلومات قاعدة البيانات -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-server"></i> معلومات قاعدة البيانات
                    </div>
                    <div class="card-body">
                        <?php foreach ($dbInfo as $key => $value): ?>
                            <?php if (!is_array($value)): ?>
                            <div class="info-item">
                                <span><?php echo $key; ?></span>
                                <span>
                                    <?php if ($key === 'Data Directory'): ?>
                                        <div class="path-display"><?php echo htmlspecialchars($value); ?></div>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($value); ?>
                                    <?php endif; ?>
                                </span>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cog"></i> معلومات النظام
                    </div>
                    <div class="card-body">
                        <?php foreach ($systemInfo as $key => $value): ?>
                        <div class="info-item">
                            <span><?php echo $key; ?></span>
                            <span><?php echo htmlspecialchars($value); ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- جداول قاعدة البيانات -->
            <?php if (isset($dbInfo['Tables']) && !empty($dbInfo['Tables'])): ?>
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-table"></i> جداول قاعدة البيانات (<?php echo count($dbInfo['Tables']); ?> جدول)
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الجدول</th>
                                <th>عدد السجلات</th>
                                <th>حجم الجدول</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dbInfo['Tables'] as $tableName => $tableInfo): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($tableName); ?></td>
                                <td><?php echo number_format($tableInfo['records']); ?></td>
                                <td><?php echo $tableInfo['size']; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>

            <!-- النشاط الأخير -->
            <?php if (isset($dbInfo['Recent Activity']) && !empty($dbInfo['Recent Activity'])): ?>
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-history"></i> النشاط الأخير
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الجدول</th>
                                <th>آخر تحديث</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($dbInfo['Recent Activity'] as $activity): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($activity['table_name']); ?></td>
                                <td><?php echo $activity['update_time']; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>

            <!-- روابط سريعة -->
            <div style="text-align: center; margin-top: 2rem;">
                <a href="http://localhost/phpmyadmin/" target="_blank" style="display: inline-block; padding: 0.75rem 1.5rem; background: #007bff; color: white; text-decoration: none; border-radius: 0.5rem; margin: 0.5rem;">
                    <i class="fas fa-external-link-alt"></i> فتح phpMyAdmin
                </a>
                
                <a href="laragon-app.php" style="display: inline-block; padding: 0.75rem 1.5rem; background: #28a745; color: white; text-decoration: none; border-radius: 0.5rem; margin: 0.5rem;">
                    <i class="fas fa-home"></i> العودة للنظام
                </a>
                
                <a href="setup-database.php" style="display: inline-block; padding: 0.75rem 1.5rem; background: #ffc107; color: #212529; text-decoration: none; border-radius: 0.5rem; margin: 0.5rem;">
                    <i class="fas fa-tools"></i> إعداد قاعدة البيانات
                </a>
            </div>
        </div>
    </div>

    <!-- زر التحديث -->
    <button class="refresh-btn" onclick="location.reload()" title="تحديث البيانات">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script>
        // تحديث تلقائي كل 10 ثوان
        setInterval(() => {
            location.reload();
        }, 10000);

        console.log('📊 مراقب قاعدة البيانات نشط');
        console.log('🔄 تحديث تلقائي كل 10 ثوان');
    </script>
</body>
</html>

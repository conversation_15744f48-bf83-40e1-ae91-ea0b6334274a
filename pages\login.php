<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

$loginError = '';
$loginSuccess = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $loginError = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? AND is_active = 1");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password'])) {
                // تسجيل الدخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['permissions'] = json_decode($user['permissions'] ?? '[]', true);
                
                // تحديث آخر دخول
                $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$user['id']]);
                
                // تسجيل النشاط
                $stmt = $pdo->prepare("INSERT INTO activity_log (user_id, action, ip_address, user_agent) VALUES (?, ?, ?, ?)");
                $stmt->execute([
                    $user['id'],
                    'تسجيل دخول',
                    $_SERVER['REMOTE_ADDR'] ?? '',
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
                
                $loginSuccess = 'تم تسجيل الدخول بنجاح';
                
                // إعادة توجيه
                echo "<script>
                    setTimeout(() => {
                        window.location.href = '?page=dashboard';
                    }, 1500);
                </script>";
                
            } else {
                $loginError = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (PDOException $e) {
            $loginError = 'خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<div style="display: flex; align-items: center; justify-content: center; min-height: 70vh;">
    <div class="card" style="max-width: 400px; width: 100%;">
        <!-- رأس النموذج -->
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;">
                <i class="fas fa-user-circle"></i>
            </div>
            <h2 style="color: #333; margin-bottom: 0.5rem;">تسجيل الدخول</h2>
            <p style="color: #666; font-size: 0.9rem;">ادخل بياناتك للوصول إلى النظام</p>
        </div>

        <!-- رسائل النظام -->
        <?php if ($loginError): ?>
        <div style="background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; border-right: 4px solid #dc3545;">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo htmlspecialchars($loginError); ?>
        </div>
        <?php endif; ?>

        <?php if ($loginSuccess): ?>
        <div style="background: #d4edda; color: #155724; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; border-right: 4px solid #28a745;">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($loginSuccess); ?>
            <div style="margin-top: 0.5rem; font-size: 0.9rem;">
                جاري إعادة التوجيه...
            </div>
        </div>
        <?php endif; ?>

        <!-- نموذج تسجيل الدخول -->
        <form method="POST" id="loginForm">
            <div style="margin-bottom: 1.5rem;">
                <label for="username" style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333;">
                    <i class="fas fa-user"></i> اسم المستخدم
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                    required
                    style="width: 100%; padding: 0.75rem; border: 2px solid #dee2e6; border-radius: 0.5rem; font-size: 1rem; transition: border-color 0.3s ease;"
                    placeholder="أدخل اسم المستخدم"
                    autocomplete="username"
                >
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label for="password" style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #333;">
                    <i class="fas fa-lock"></i> كلمة المرور
                </label>
                <div style="position: relative;">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        required
                        style="width: 100%; padding: 0.75rem; border: 2px solid #dee2e6; border-radius: 0.5rem; font-size: 1rem; transition: border-color 0.3s ease; padding-left: 3rem;"
                        placeholder="أدخل كلمة المرور"
                        autocomplete="current-password"
                    >
                    <button 
                        type="button" 
                        onclick="togglePassword()" 
                        style="position: absolute; left: 0.75rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer; font-size: 1.1rem;"
                        title="إظهار/إخفاء كلمة المرور"
                    >
                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer; font-size: 0.9rem; color: #666;">
                    <input type="checkbox" name="remember" style="margin: 0;">
                    تذكرني لمدة 30 يوم
                </label>
            </div>

            <button 
                type="submit" 
                name="login"
                class="btn btn-success" 
                style="width: 100%; padding: 1rem; font-size: 1.1rem; font-weight: 600;"
                id="loginButton"
            >
                <i class="fas fa-sign-in-alt"></i>
                <span id="loginButtonText">تسجيل الدخول</span>
            </button>
        </form>

        <!-- روابط إضافية -->
        <div style="text-align: center; margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid #dee2e6;">
            <a href="?page=welcome" style="color: #667eea; text-decoration: none; font-size: 0.9rem;">
                <i class="fas fa-arrow-right"></i> العودة للصفحة الرئيسية
            </a>
        </div>

        <!-- بيانات تجريبية -->
        <div style="background: #f8f9fa; border-radius: 0.5rem; padding: 1rem; margin-top: 1.5rem;">
            <h5 style="color: #495057; margin-bottom: 0.75rem; font-size: 0.9rem;">
                <i class="fas fa-info-circle"></i> حسابات تجريبية:
            </h5>
            <div style="font-size: 0.8rem; color: #6c757d; line-height: 1.4;">
                <div style="margin-bottom: 0.25rem;"><strong>المدير:</strong> admin / admin123</div>
                <div style="margin-bottom: 0.25rem;"><strong>المحاسب:</strong> accountant / admin123</div>
                <div style="margin-bottom: 0.25rem;"><strong>الكاشير:</strong> cashier1 / admin123</div>
                <div><strong>المدير:</strong> manager / admin123</div>
            </div>
        </div>
    </div>
</div>

<script>
// وظائف تسجيل الدخول
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginButton = document.getElementById('loginButton');
    const loginButtonText = document.getElementById('loginButtonText');
    
    // تركيز تلقائي على حقل اسم المستخدم
    usernameInput.focus();
    
    // تأثيرات التركيز على الحقول
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = '#667eea';
            this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
        });
        
        input.addEventListener('blur', function() {
            this.style.borderColor = '#dee2e6';
            this.style.boxShadow = 'none';
        });
    });
    
    // معالجة إرسال النموذج
    form.addEventListener('submit', function(e) {
        const username = usernameInput.value.trim();
        const password = passwordInput.value;
        
        if (!username || !password) {
            e.preventDefault();
            alert('يرجى إدخال جميع البيانات المطلوبة');
            return;
        }
        
        // تغيير حالة الزر
        loginButton.disabled = true;
        loginButtonText.textContent = 'جاري تسجيل الدخول...';
        loginButton.style.opacity = '0.7';
        
        // إضافة أيقونة التحميل
        const spinner = document.createElement('i');
        spinner.className = 'fas fa-spinner fa-spin';
        spinner.style.marginLeft = '0.5rem';
        loginButton.insertBefore(spinner, loginButtonText);
    });
    
    // ملء سريع للحسابات التجريبية
    document.querySelectorAll('[data-username]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const username = this.dataset.username;
            const password = this.dataset.password;
            
            usernameInput.value = username;
            passwordInput.value = password;
            
            // تأثير بصري
            usernameInput.style.background = '#e8f5e8';
            passwordInput.style.background = '#e8f5e8';
            
            setTimeout(() => {
                usernameInput.style.background = '';
                passwordInput.style.background = '';
            }, 1000);
        });
    });
    
    console.log('🔐 صفحة تسجيل الدخول جاهزة');
});

// اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // Enter للإرسال
    if (e.key === 'Enter' && (e.target.id === 'username' || e.target.id === 'password')) {
        document.getElementById('loginForm').submit();
    }
    
    // Escape للعودة
    if (e.key === 'Escape') {
        window.location.href = '?page=welcome';
    }
});

// إضافة روابط سريعة للحسابات التجريبية
document.addEventListener('DOMContentLoaded', function() {
    const testAccounts = document.querySelector('[style*="background: #f8f9fa"]');
    if (testAccounts) {
        const accounts = [
            { username: 'admin', password: 'admin123', title: 'المدير' },
            { username: 'accountant', password: 'admin123', title: 'المحاسب' },
            { username: 'cashier1', password: 'admin123', title: 'الكاشير' },
            { username: 'manager', password: 'admin123', title: 'المدير' }
        ];
        
        const quickLinks = document.createElement('div');
        quickLinks.style.marginTop = '0.5rem';
        quickLinks.innerHTML = '<small style="color: #6c757d;">انقر للملء السريع:</small>';
        
        accounts.forEach(account => {
            const link = document.createElement('a');
            link.href = '#';
            link.textContent = account.title;
            link.dataset.username = account.username;
            link.dataset.password = account.password;
            link.style.cssText = 'color: #667eea; text-decoration: none; margin: 0 0.5rem; font-size: 0.8rem;';
            link.addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('username').value = this.dataset.username;
                document.getElementById('password').value = this.dataset.password;
            });
            quickLinks.appendChild(link);
        });
        
        testAccounts.appendChild(quickLinks);
    }
});
</script>

<style>
/* تحسينات CSS لصفحة تسجيل الدخول */
.card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

input:focus {
    outline: none !important;
}

.btn:disabled {
    cursor: not-allowed;
}

/* تأثيرات الهوفر */
input:hover {
    border-color: #adb5bd !important;
}

/* تحسين الاستجابة */
@media (max-width: 480px) {
    .card {
        margin: 1rem;
        padding: 1.5rem;
    }
}
</style>

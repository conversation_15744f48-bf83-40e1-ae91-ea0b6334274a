@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    تثبيت نظام إدارة المخبز في Laragon
echo ========================================
echo.

REM التحقق من وجود Laragon
if not exist "C:\laragon" (
    echo ❌ لم يتم العثور على Laragon في المسار الافتراضي
    echo    يرجى التأكد من تثبيت Laragon أولاً
    pause
    exit /b 1
)

REM إنشاء مجلد المشروع
set PROJECT_DIR=C:\laragon\www\bakery-system
echo 📁 إنشاء مجلد المشروع...
if not exist "%PROJECT_DIR%" (
    mkdir "%PROJECT_DIR%"
    echo ✅ تم إنشاء المجلد: %PROJECT_DIR%
) else (
    echo ⚠️  المجلد موجود مسبقاً: %PROJECT_DIR%
)

REM نسخ الملفات
echo.
echo 📋 نسخ ملفات النظام...

if exist "laragon-app.php" (
    copy "laragon-app.php" "%PROJECT_DIR%\" >nul
    echo ✅ تم نسخ: laragon-app.php
) else (
    echo ❌ لم يتم العثور على: laragon-app.php
)

if exist "setup-database.php" (
    copy "setup-database.php" "%PROJECT_DIR%\" >nul
    echo ✅ تم نسخ: setup-database.php
) else (
    echo ❌ لم يتم العثور على: setup-database.php
)

if exist "offline-app.html" (
    copy "offline-app.html" "%PROJECT_DIR%\" >nul
    echo ✅ تم نسخ: offline-app.html
) else (
    echo ❌ لم يتم العثور على: offline-app.html
)

REM إنشاء ملف index.php للتوجيه
echo ^<?php header('Location: laragon-app.php'); ?^> > "%PROJECT_DIR%\index.php"
echo ✅ تم إنشاء: index.php

REM التحقق من حالة Laragon
echo.
echo 🔍 فحص حالة Laragon...

tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Apache يعمل
    set APACHE_RUNNING=1
) else (
    echo ❌ Apache لا يعمل
    set APACHE_RUNNING=0
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ MySQL يعمل
    set MYSQL_RUNNING=1
) else (
    echo ❌ MySQL لا يعمل
    set MYSQL_RUNNING=0
)

echo.
echo ========================================
echo           نتائج التثبيت
echo ========================================

if %APACHE_RUNNING%==1 if %MYSQL_RUNNING%==1 (
    echo ✅ التثبيت مكتمل بنجاح!
    echo.
    echo 🌐 افتح المتصفح واذهب إلى:
    echo    http://localhost/bakery-system/
    echo.
    echo 📋 أو:
    echo    http://localhost/bakery-system/laragon-app.php
    echo.
    echo 🔑 بيانات تسجيل الدخول الافتراضية:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin123
    echo.
    
    REM فتح المتصفح تلقائياً
    choice /C YN /M "هل تريد فتح التطبيق في المتصفح الآن؟ (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 (
        echo 🚀 فتح التطبيق...
        start http://localhost/bakery-system/
    )
    
) else (
    echo ⚠️  التثبيت مكتمل لكن Laragon لا يعمل بالكامل
    echo.
    echo 🔧 يرجى:
    echo    1. فتح Laragon
    echo    2. الضغط على "Start All"
    echo    3. ثم فتح: http://localhost/bakery-system/
    echo.
    
    choice /C YN /M "هل تريد فتح Laragon الآن؟ (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 (
        echo 🚀 فتح Laragon...
        if exist "C:\laragon\laragon.exe" (
            start "Laragon" "C:\laragon\laragon.exe"
        ) else (
            echo ❌ لم يتم العثور على Laragon.exe
        )
    )
)

:end
echo.
echo 📞 للدعم الفني: أنوار سوفت
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
pause

<?php
/**
 * معالج إعداد قاعدة البيانات التلقائي لـ Laragon
 * Automatic Database Setup Handler for Laragon
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakery_system');
define('DB_USER', 'root');
define('DB_PASS', '');

$setupSteps = [];
$errors = [];
$success = false;

// الخطوة 1: إنشاء قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $setupSteps[] = "✅ تم إنشاء قاعدة البيانات: " . DB_NAME;
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
} catch (PDOException $e) {
    $errors[] = "❌ خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
}

// الخطوة 2: إنشاء الجداول
if (empty($errors)) {
    try {
        // جدول المستخدمين
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                role ENUM('admin', 'manager', 'employee', 'accountant') DEFAULT 'employee',
                permissions JSON,
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول المستخدمين";

        // جدول بيانات الشركة
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS company_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                company_name VARCHAR(200) NOT NULL,
                company_name_en VARCHAR(200),
                address TEXT,
                phone VARCHAR(50),
                mobile VARCHAR(50),
                email VARCHAR(100),
                website VARCHAR(100),
                tax_number VARCHAR(50),
                commercial_register VARCHAR(50),
                currency VARCHAR(10) DEFAULT 'YER',
                currency_symbol VARCHAR(10) DEFAULT 'ر.ي',
                decimal_places INT DEFAULT 2,
                logo_path VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول بيانات الشركة";

        // جدول شجرة الحسابات
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS chart_of_accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_code VARCHAR(20) UNIQUE NOT NULL,
                account_name VARCHAR(200) NOT NULL,
                parent_id INT NULL,
                account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
                account_level INT DEFAULT 1,
                opening_balance DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES chart_of_accounts(id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول شجرة الحسابات";

        // جدول الصناديق
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS cash_boxes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                box_name VARCHAR(100) NOT NULL,
                box_code VARCHAR(20) UNIQUE NOT NULL,
                account_id INT NOT NULL,
                responsible_user_id INT,
                opening_balance DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (responsible_user_id) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول الصناديق";

        // جدول البنوك
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS banks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                bank_name VARCHAR(100) NOT NULL,
                account_number VARCHAR(50) UNIQUE NOT NULL,
                account_name VARCHAR(100) NOT NULL,
                account_id INT NOT NULL,
                branch VARCHAR(100),
                iban VARCHAR(50),
                swift_code VARCHAR(20),
                opening_balance DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول البنوك";

        // جدول الموظفين
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS employees (
                id INT AUTO_INCREMENT PRIMARY KEY,
                employee_code VARCHAR(20) UNIQUE NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                national_id VARCHAR(20) UNIQUE,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                job_title VARCHAR(100) NOT NULL,
                department VARCHAR(100),
                hire_date DATE NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                allowances DECIMAL(10,2) DEFAULT 0,
                account_id INT,
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول الموظفين";

        // جدول فئات الأصناف
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                category_name VARCHAR(100) NOT NULL,
                category_code VARCHAR(20) UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول فئات الأصناف";

        // جدول الأصناف
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                item_name VARCHAR(200) NOT NULL,
                item_code VARCHAR(50) UNIQUE NOT NULL,
                category_id INT NOT NULL,
                unit VARCHAR(20) NOT NULL,
                current_stock DECIMAL(10,3) DEFAULT 0,
                min_stock DECIMAL(10,3) DEFAULT 0,
                max_stock DECIMAL(10,3) DEFAULT 0,
                cost_price DECIMAL(10,2) DEFAULT 0,
                selling_price DECIMAL(10,2) DEFAULT 0,
                barcode VARCHAR(50),
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        $setupSteps[] = "✅ تم إنشاء جدول الأصناف";

    } catch (PDOException $e) {
        $errors[] = "❌ خطأ في إنشاء الجداول: " . $e->getMessage();
    }
}

// الخطوة 3: إدراج البيانات الأولية
if (empty($errors)) {
    try {
        // إنشاء المستخدم الافتراضي
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("
            INSERT IGNORE INTO users (username, password, full_name, email, role, permissions) 
            VALUES ('admin', '$hashedPassword', 'مدير النظام', '<EMAIL>', 'admin', '{\"all\": true}')
        ");
        $setupSteps[] = "✅ تم إنشاء المستخدم الافتراضي (admin/admin123)";

        // إدراج بيانات الشركة الافتراضية
        $pdo->exec("
            INSERT IGNORE INTO company_settings (company_name, company_name_en, address, phone, email, currency, currency_symbol) 
            VALUES ('مخبز أنوار', 'Anwar Bakery', 'صنعاء - اليمن', '01-123456', '<EMAIL>', 'YER', 'ر.ي')
        ");
        $setupSteps[] = "✅ تم إدراج بيانات الشركة الافتراضية";

        // إنشاء شجرة الحسابات الأساسية
        $accounts = [
            ['1', 'الأصول', null, 'asset', 1],
            ['2', 'الخصوم', null, 'liability', 1],
            ['3', 'حقوق الملكية', null, 'equity', 1],
            ['4', 'الإيرادات', null, 'revenue', 1],
            ['5', 'المصروفات', null, 'expense', 1],
            ['11', 'الأصول المتداولة', 1, 'asset', 2],
            ['111', 'النقدية والبنوك', 6, 'asset', 3],
            ['1111', 'الصناديق', 7, 'asset', 4],
            ['1112', 'البنوك', 7, 'asset', 4]
        ];

        foreach ($accounts as $account) {
            $pdo->exec("
                INSERT IGNORE INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level) 
                VALUES ('{$account[0]}', '{$account[1]}', " . ($account[2] ? $account[2] : 'NULL') . ", '{$account[3]}', {$account[4]})
            ");
        }
        $setupSteps[] = "✅ تم إنشاء شجرة الحسابات الأساسية";

        // إنشاء فئات أصناف افتراضية
        $categories = [
            ['خامات الخبز', 'CAT001'],
            ['منتجات جاهزة', 'CAT002'],
            ['مواد تعبئة', 'CAT003']
        ];

        foreach ($categories as $category) {
            $pdo->exec("
                INSERT IGNORE INTO categories (category_name, category_code, created_by) 
                VALUES ('{$category[0]}', '{$category[1]}', 1)
            ");
        }
        $setupSteps[] = "✅ تم إنشاء فئات الأصناف الافتراضية";

        $success = true;

    } catch (PDOException $e) {
        $errors[] = "❌ خطأ في إدراج البيانات الأولية: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - نظام إدارة المخبز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .step {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid #28a745;
            background: #f8f9fa;
        }

        .error {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            text-align: center;
            border: 1px solid #c3e6cb;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-database"></i> إعداد قاعدة البيانات</h1>
            <p>إعداد تلقائي لقاعدة البيانات والجداول</p>
        </div>
        
        <div class="content">
            <?php if ($success): ?>
                <div class="success-message">
                    <h3><i class="fas fa-check-circle"></i> تم الإعداد بنجاح!</h3>
                    <p>تم إنشاء قاعدة البيانات والجداول بنجاح. النظام جاهز للاستخدام.</p>
                    
                    <div style="margin: 1rem 0; padding: 1rem; background: #fff3cd; border-radius: 0.5rem;">
                        <strong>بيانات تسجيل الدخول:</strong><br>
                        اسم المستخدم: <code>admin</code><br>
                        كلمة المرور: <code>admin123</code>
                    </div>
                    
                    <a href="laragon-app.php" class="btn">
                        <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                    </a>
                    <a href="login.php" class="btn">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <h3><i class="fas fa-exclamation-triangle"></i> الأخطاء:</h3>
                <?php foreach ($errors as $error): ?>
                    <div class="error"><?php echo $error; ?></div>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (!empty($setupSteps)): ?>
                <h3><i class="fas fa-list-check"></i> خطوات الإعداد:</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo (count($setupSteps) / 10) * 100; ?>%"></div>
                </div>
                <?php foreach ($setupSteps as $step): ?>
                    <div class="step"><?php echo $step; ?></div>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (!$success && empty($errors)): ?>
                <p>جاري إعداد قاعدة البيانات...</p>
                <script>
                    setTimeout(() => location.reload(), 2000);
                </script>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

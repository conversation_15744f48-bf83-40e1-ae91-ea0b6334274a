<?php
/**
 * وحدة إدارة الموظفين
 * Employees Management Module
 */

// تضمين الملفات المطلوبة
require_once '../../config/database.php';
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/validation.php';

// التحقق من الصلاحيات
requirePermission('employees', 'view');

$db = getDB();
$error = '';
$success = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add':
            if (!hasPermission('employees', 'add')) {
                $error = 'ليس لديك صلاحية لإضافة موظف جديد';
                break;
            }

            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('employee_code', 'رمز الموظف مطلوب')
                     ->required('full_name', 'الاسم الكامل مطلوب')
                     ->required('job_title', 'المسمى الوظيفي مطلوب')
                     ->required('hire_date', 'تاريخ التعيين مطلوب')
                     ->required('basic_salary', 'الراتب الأساسي مطلوب')
                     ->unique('employee_code', 'employees', 'employee_code', null, 'رمز الموظف موجود مسبقاً')
                     ->unique('national_id', 'employees', 'national_id', null, 'رقم الهوية موجود مسبقاً')
                     ->email('email', 'البريد الإلكتروني غير صحيح')
                     ->phone('phone', 'رقم الهاتف غير صحيح')
                     ->numeric('basic_salary', 'الراتب الأساسي يجب أن يكون رقم')
                     ->numeric('allowances', 'البدلات يجب أن تكون رقم')
                     ->date('hire_date', 'Y-m-d', 'تاريخ التعيين غير صحيح');

            if (!$validator->hasErrors()) {
                try {
                    $db->beginTransaction();

                    // إنشاء حساب للموظف في شجرة الحسابات
                    $employee_account_parent = $db->selectOne(
                        "SELECT id FROM chart_of_accounts WHERE account_code = '212' LIMIT 1"
                    );

                    if (!$employee_account_parent) {
                        throw new Exception('لم يتم العثور على حساب رواتب الموظفين الرئيسي');
                    }

                    // توليد رمز حساب فرعي
                    $last_account = $db->selectOne(
                        "SELECT account_code FROM chart_of_accounts
                         WHERE parent_id = ? AND account_code LIKE '212%'
                         ORDER BY account_code DESC LIMIT 1",
                        [$employee_account_parent['id']]
                    );

                    $next_code = $last_account ?
                        (intval(substr($last_account['account_code'], 3)) + 1) : 1;
                    $account_code = '212' . str_pad($next_code, 3, '0', STR_PAD_LEFT);

                    // إنشاء الحساب
                    $account_id = $db->insert(
                        "INSERT INTO chart_of_accounts (account_code, account_name, parent_id, account_type, account_level, created_by)
                         VALUES (?, ?, ?, 'liability', 4, ?)",
                        [
                            $account_code,
                            'راتب ' . cleanInput($_POST['full_name']),
                            $employee_account_parent['id'],
                            $_SESSION['user_id']
                        ]
                    );

                    // إنشاء الموظف
                    $employee_id = $db->insert(
                        "INSERT INTO employees (employee_code, full_name, national_id, phone, email, address, job_title, department, hire_date, basic_salary, allowances, account_id, notes, created_by)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        [
                            cleanInput($_POST['employee_code']),
                            cleanInput($_POST['full_name']),
                            cleanInput($_POST['national_id']),
                            cleanInput($_POST['phone']),
                            cleanInput($_POST['email']),
                            cleanInput($_POST['address']),
                            cleanInput($_POST['job_title']),
                            cleanInput($_POST['department']),
                            $_POST['hire_date'],
                            floatval($_POST['basic_salary']),
                            floatval($_POST['allowances'] ?? 0),
                            $account_id,
                            cleanInput($_POST['notes']),
                            $_SESSION['user_id']
                        ]
                    );

                    $db->commit();

                    logUserActivity('create', 'employees', $employee_id, null, $_POST);
                    $success = 'تم إضافة الموظف بنجاح';

                } catch (Exception $e) {
                    $db->rollback();
                    $error = 'خطأ في إضافة الموظف: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;

        case 'edit':
            if (!hasPermission('employees', 'edit')) {
                $error = 'ليس لديك صلاحية لتعديل الموظف';
                break;
            }

            $employee_id = intval($_POST['employee_id']);

            // التحقق من صحة البيانات
            $validator = validator($_POST);
            $validator->required('full_name', 'الاسم الكامل مطلوب')
                     ->required('job_title', 'المسمى الوظيفي مطلوب')
                     ->required('basic_salary', 'الراتب الأساسي مطلوب')
                     ->unique('employee_code', 'employees', 'employee_code', $employee_id, 'رمز الموظف موجود مسبقاً')
                     ->unique('national_id', 'employees', 'national_id', $employee_id, 'رقم الهوية موجود مسبقاً')
                     ->email('email', 'البريد الإلكتروني غير صحيح')
                     ->phone('phone', 'رقم الهاتف غير صحيح')
                     ->numeric('basic_salary', 'الراتب الأساسي يجب أن يكون رقم')
                     ->numeric('allowances', 'البدلات يجب أن تكون رقم');

            if (!$validator->hasErrors()) {
                try {
                    $old_data = $db->selectOne("SELECT * FROM employees WHERE id = ?", [$employee_id]);

                    $db->update(
                        "UPDATE employees SET full_name = ?, national_id = ?, phone = ?, email = ?, address = ?, job_title = ?, department = ?, basic_salary = ?, allowances = ?, notes = ?, is_active = ?, updated_at = NOW() WHERE id = ?",
                        [
                            cleanInput($_POST['full_name']),
                            cleanInput($_POST['national_id']),
                            cleanInput($_POST['phone']),
                            cleanInput($_POST['email']),
                            cleanInput($_POST['address']),
                            cleanInput($_POST['job_title']),
                            cleanInput($_POST['department']),
                            floatval($_POST['basic_salary']),
                            floatval($_POST['allowances'] ?? 0),
                            cleanInput($_POST['notes']),
                            isset($_POST['is_active']) ? 1 : 0,
                            $employee_id
                        ]
                    );

                    // تحديث اسم الحساب المرتبط
                    if ($old_data['account_id']) {
                        $db->update(
                            "UPDATE chart_of_accounts SET account_name = ? WHERE id = ?",
                            ['راتب ' . cleanInput($_POST['full_name']), $old_data['account_id']]
                        );
                    }

                    logUserActivity('update', 'employees', $employee_id, $old_data, $_POST);
                    $success = 'تم تحديث بيانات الموظف بنجاح';

                } catch (Exception $e) {
                    $error = 'خطأ في تحديث بيانات الموظف: ' . $e->getMessage();
                }
            } else {
                $error = $validator->getFirstError();
            }
            break;

        case 'toggle_status':
            if (!hasPermission('employees', 'edit')) {
                $error = 'ليس لديك صلاحية لتغيير حالة الموظف';
                break;
            }

            $employee_id = intval($_POST['employee_id']);

            try {
                $employee = $db->selectOne("SELECT is_active FROM employees WHERE id = ?", [$employee_id]);
                $new_status = $employee['is_active'] ? 0 : 1;

                $db->update("UPDATE employees SET is_active = ? WHERE id = ?", [$new_status, $employee_id]);

                logUserActivity($new_status ? 'activate' : 'deactivate', 'employees', $employee_id);
                $success = $new_status ? 'تم تفعيل الموظف' : 'تم إيقاف الموظف';

            } catch (Exception $e) {
                $error = 'خطأ في تغيير حالة الموظف: ' . $e->getMessage();
            }
            break;
    }
}

// جلب قائمة الموظفين
try {
    $employees = $db->select(
        "SELECT e.*, creator.full_name as created_by_name
         FROM employees e
         LEFT JOIN users creator ON e.created_by = creator.id
         ORDER BY e.is_active DESC, e.full_name"
    );

    // حساب الإحصائيات
    $stats = [
        'total' => count($employees),
        'active' => count(array_filter($employees, fn($e) => $e['is_active'])),
        'inactive' => count(array_filter($employees, fn($e) => !$e['is_active'])),
        'total_salaries' => array_sum(array_map(fn($e) => $e['basic_salary'] + $e['allowances'], $employees))
    ];

} catch (Exception $e) {
    $error = 'خطأ في تحميل البيانات: ' . $e->getMessage();
}

$pageTitle = 'إدارة الموظفين';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="../../assets/css/style.css">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .employee-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745, #20c997);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .salary-amount {
            font-weight: bold;
            color: #28a745;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 2% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .employee-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .employee-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- الشريط الجانبي -->
    <?php include '../../includes/sidebar.php'; ?>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الهيدر -->
        <header class="header">
            <div class="d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-user-tie"></i> <?php echo $pageTitle; ?></h1>
                <div>
                    <?php if (hasPermission('employees', 'add')): ?>
                    <button type="button" class="btn btn-primary" onclick="openAddModal()">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </button>
                    <?php endif; ?>

                    <a href="salaries.php" class="btn btn-success">
                        <i class="fas fa-money-bill-wave"></i> إدارة الرواتب
                    </a>
                </div>
            </div>
        </header>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h6>إجمالي الموظفين</h6>
                    <div class="stats-number"><?php echo $stats['total'] ?? 0; ?></div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h6>الموظفين النشطين</h6>
                    <div class="stats-number"><?php echo $stats['active'] ?? 0; ?></div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-user-times fa-2x mb-2"></i>
                    <h6>الموظفين المتوقفين</h6>
                    <div class="stats-number"><?php echo $stats['inactive'] ?? 0; ?></div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <h6>إجمالي الرواتب</h6>
                    <div class="stats-number"><?php echo formatMoney($stats['total_salaries'] ?? 0, false); ?></div>
                </div>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> قائمة الموظفين</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>رمز الموظف</th>
                                <th>المسمى الوظيفي</th>
                                <th>القسم</th>
                                <th>تاريخ التعيين</th>
                                <th>الراتب الأساسي</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($employees)): ?>
                                <?php foreach ($employees as $employee): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="employee-avatar">
                                                <?php echo strtoupper(substr($employee['full_name'], 0, 1)); ?>
                                            </div>
                                            <div class="ms-3">
                                                <strong><?php echo htmlspecialchars($employee['full_name']); ?></strong>
                                                <?php if ($employee['phone']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($employee['phone']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($employee['employee_code']); ?></td>
                                    <td><?php echo htmlspecialchars($employee['job_title']); ?></td>
                                    <td><?php echo htmlspecialchars($employee['department'] ?? '-'); ?></td>
                                    <td><?php echo formatDate($employee['hire_date']); ?></td>
                                    <td>
                                        <span class="salary-amount"><?php echo formatMoney($employee['basic_salary']); ?></span>
                                        <?php if ($employee['allowances'] > 0): ?>
                                        <br><small class="text-muted">+ <?php echo formatMoney($employee['allowances']); ?> بدلات</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-badge <?php echo $employee['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $employee['is_active'] ? 'نشط' : 'متوقف'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (hasPermission('employees', 'edit')): ?>
                                        <button type="button" class="btn btn-sm btn-warning"
                                                onclick="openEditModal(<?php echo htmlspecialchars(json_encode($employee)); ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="employee_id" value="<?php echo $employee['id']; ?>">
                                            <button type="submit" class="btn btn-sm <?php echo $employee['is_active'] ? 'btn-danger' : 'btn-success'; ?>"
                                                    onclick="return confirm('هل أنت متأكد من تغيير حالة هذا الموظف؟')">
                                                <i class="fas fa-<?php echo $employee['is_active'] ? 'ban' : 'check'; ?>"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>

                                        <button type="button" class="btn btn-sm btn-info"
                                                onclick="viewEmployeeDetails(<?php echo $employee['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>

                                        <a href="salary_history.php?employee_id=<?php echo $employee['id']; ?>"
                                           class="btn btn-sm btn-success">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">لا توجد موظفين مسجلين</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- نافذة إضافة موظف جديد -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('addModal')">&times;</span>
            <h3><i class="fas fa-plus"></i> إضافة موظف جديد</h3>

            <form method="POST" action="">
                <input type="hidden" name="action" value="add">

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="employee_code" class="form-label">رمز الموظف *</label>
                            <input type="text" name="employee_code" id="employee_code" class="form-control" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" name="full_name" id="full_name" class="form-control" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="national_id" class="form-label">رقم الهوية</label>
                            <input type="text" name="national_id" id="national_id" class="form-control">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" id="phone" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="email" id="email" class="form-control">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="hire_date" class="form-label">تاريخ التعيين *</label>
                            <input type="date" name="hire_date" id="hire_date" class="form-control" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea name="address" id="address" class="form-control" rows="2"></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="job_title" class="form-label">المسمى الوظيفي *</label>
                            <input type="text" name="job_title" id="job_title" class="form-control" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="department" class="form-label">القسم</label>
                            <input type="text" name="department" id="department" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="basic_salary" class="form-label">الراتب الأساسي *</label>
                            <input type="number" name="basic_salary" id="basic_salary" class="form-control" step="0.01" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="allowances" class="form-label">البدلات</label>
                            <input type="number" name="allowances" id="allowances" class="form-control" step="0.01" value="0">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                </div>

                <div class="form-group text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الموظف
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تعديل الموظف -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('editModal')">&times;</span>
            <h3><i class="fas fa-edit"></i> تعديل بيانات الموظف</h3>

            <form method="POST" action="">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="employee_id" id="edit_employee_id">

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_employee_code" class="form-label">رمز الموظف</label>
                            <input type="text" name="employee_code" id="edit_employee_code" class="form-control" readonly>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" name="full_name" id="edit_full_name" class="form-control" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_national_id" class="form-label">رقم الهوية</label>
                            <input type="text" name="national_id" id="edit_national_id" class="form-control">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_phone" class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" id="edit_phone" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="email" id="edit_email" class="form-control">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" id="edit_is_active" class="form-check-input">
                                <label for="edit_is_active" class="form-check-label">موظف نشط</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit_address" class="form-label">العنوان</label>
                    <textarea name="address" id="edit_address" class="form-control" rows="2"></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_job_title" class="form-label">المسمى الوظيفي *</label>
                            <input type="text" name="job_title" id="edit_job_title" class="form-control" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_department" class="form-label">القسم</label>
                            <input type="text" name="department" id="edit_department" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_basic_salary" class="form-label">الراتب الأساسي *</label>
                            <input type="number" name="basic_salary" id="edit_basic_salary" class="form-control" step="0.01" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="edit_allowances" class="form-label">البدلات</label>
                            <input type="number" name="allowances" id="edit_allowances" class="form-control" step="0.01">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit_notes" class="form-label">ملاحظات</label>
                    <textarea name="notes" id="edit_notes" class="form-control" rows="3"></textarea>
                </div>

                <div class="form-group text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openAddModal() {
            // توليد رمز موظف تلقائي
            const code = 'EMP' + String(Date.now()).slice(-4);
            document.getElementById('employee_code').value = code;

            // تعيين تاريخ اليوم كتاريخ افتراضي للتعيين
            document.getElementById('hire_date').value = new Date().toISOString().split('T')[0];

            document.getElementById('addModal').style.display = 'block';
        }

        function openEditModal(employee) {
            document.getElementById('edit_employee_id').value = employee.id;
            document.getElementById('edit_employee_code').value = employee.employee_code;
            document.getElementById('edit_full_name').value = employee.full_name;
            document.getElementById('edit_national_id').value = employee.national_id || '';
            document.getElementById('edit_phone').value = employee.phone || '';
            document.getElementById('edit_email').value = employee.email || '';
            document.getElementById('edit_address').value = employee.address || '';
            document.getElementById('edit_job_title').value = employee.job_title;
            document.getElementById('edit_department').value = employee.department || '';
            document.getElementById('edit_basic_salary').value = employee.basic_salary;
            document.getElementById('edit_allowances').value = employee.allowances || 0;
            document.getElementById('edit_notes').value = employee.notes || '';
            document.getElementById('edit_is_active').checked = employee.is_active == 1;

            document.getElementById('editModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function viewEmployeeDetails(employeeId) {
            window.open(`employee_details.php?id=${employeeId}`, '_blank');
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // حساب إجمالي الراتب تلقائياً
        function calculateTotalSalary(basicSalaryId, allowancesId, totalId) {
            const basicSalary = parseFloat(document.getElementById(basicSalaryId).value) || 0;
            const allowances = parseFloat(document.getElementById(allowancesId).value) || 0;
            const total = basicSalary + allowances;

            if (document.getElementById(totalId)) {
                document.getElementById(totalId).textContent = formatMoney(total);
            }
        }

        // ربط الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // للنموذج الجديد
            document.getElementById('basic_salary').addEventListener('input', function() {
                calculateTotalSalary('basic_salary', 'allowances', 'total_salary_display');
            });

            document.getElementById('allowances').addEventListener('input', function() {
                calculateTotalSalary('basic_salary', 'allowances', 'total_salary_display');
            });

            // للنموذج التعديل
            document.getElementById('edit_basic_salary').addEventListener('input', function() {
                calculateTotalSalary('edit_basic_salary', 'edit_allowances', 'edit_total_salary_display');
            });

            document.getElementById('edit_allowances').addEventListener('input', function() {
                calculateTotalSalary('edit_basic_salary', 'edit_allowances', 'edit_total_salary_display');
            });
        });

        // دالة تنسيق المبلغ
        function formatMoney(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount) + ' ر.ي';
        }
    </script>
</body>
</html>

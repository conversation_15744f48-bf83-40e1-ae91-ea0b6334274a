<?php
/**
 * أداة تشخيص وإصلاح النظام
 * System Diagnosis and Fix Tool
 */

$diagnostics = [];
$fixes = [];
$errors = [];

// فحص 1: حالة الخادم
$diagnostics['Server Status'] = [];

// فحص Apache
if (isset($_SERVER['SERVER_SOFTWARE'])) {
    $diagnostics['Server Status']['Apache'] = '✅ يعمل - ' . $_SERVER['SERVER_SOFTWARE'];
} else {
    $diagnostics['Server Status']['Apache'] = '❌ لا يعمل';
    $errors[] = 'Apache غير نشط';
}

// فحص PHP
$diagnostics['Server Status']['PHP'] = '✅ يعمل - ' . phpversion();

// فحص 2: المجلدات والملفات
$laragonPath = 'C:\\laragon\\www\\';
$projectPath = $laragonPath . 'bakery-system\\';

$diagnostics['Paths'] = [];
$diagnostics['Paths']['Laragon WWW'] = is_dir($laragonPath) ? '✅ موجود' : '❌ غير موجود';
$diagnostics['Paths']['Project Folder'] = is_dir($projectPath) ? '✅ موجود' : '❌ غير موجود';

// إنشاء المجلد إذا لم يكن موجوداً
if (!is_dir($projectPath)) {
    if (mkdir($projectPath, 0755, true)) {
        $fixes[] = '✅ تم إنشاء مجلد المشروع: ' . $projectPath;
        $diagnostics['Paths']['Project Folder'] = '✅ تم إنشاؤه';
    } else {
        $errors[] = 'فشل في إنشاء مجلد المشروع';
    }
}

// فحص الملفات المطلوبة
$requiredFiles = [
    'index.php' => '<?php header("Location: laragon-app.php"); exit(); ?>',
    'laragon-app.php' => null, // سيتم إنشاؤه لاحقاً
    'setup-database.php' => null,
    'offline-app.html' => null
];

$diagnostics['Files'] = [];

foreach ($requiredFiles as $filename => $defaultContent) {
    $filePath = $projectPath . $filename;
    
    if (file_exists($filePath)) {
        $diagnostics['Files'][$filename] = '✅ موجود';
    } else {
        $diagnostics['Files'][$filename] = '❌ مفقود';
        
        // إنشاء الملفات الأساسية
        if ($filename === 'index.php' && $defaultContent) {
            if (file_put_contents($filePath, $defaultContent)) {
                $fixes[] = "✅ تم إنشاء: $filename";
                $diagnostics['Files'][$filename] = '✅ تم إنشاؤه';
            }
        }
    }
}

// فحص 3: قاعدة البيانات
$diagnostics['Database'] = [];

try {
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $diagnostics['Database']['MySQL Connection'] = '✅ متصل';
    
    // فحص قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'bakery_system'");
    if ($stmt->rowCount() > 0) {
        $diagnostics['Database']['bakery_system'] = '✅ موجودة';
    } else {
        $diagnostics['Database']['bakery_system'] = '⚠️ غير موجودة';
    }
    
} catch (PDOException $e) {
    $diagnostics['Database']['MySQL Connection'] = '❌ فشل الاتصال';
    $errors[] = 'MySQL غير متاح: ' . $e->getMessage();
}

// فحص 4: الصلاحيات
$diagnostics['Permissions'] = [];
$diagnostics['Permissions']['Write Access'] = is_writable($projectPath) ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة';

// إنشاء ملف laragon-app.php إذا لم يكن موجوداً
if (!file_exists($projectPath . 'laragon-app.php')) {
    $laragonAppContent = '<?php
/**
 * النظام المحاسبي الهجين للمخابز - محسن لـ Laragon
 */

session_start();

define("APP_NAME", "نظام إدارة المخبز المحاسبي");
define("APP_VERSION", "2.0.0");
define("DB_HOST", "localhost");
define("DB_NAME", "bakery_system");
define("DB_USER", "root");
define("DB_PASS", "");

$isLaragon = isset($_SERVER["HTTP_HOST"]) && ($_SERVER["HTTP_HOST"] === "localhost" || strpos($_SERVER["HTTP_HOST"], ".test") !== false);
$dbAvailable = false;
$tablesExist = false;

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbAvailable = true;
    
    $stmt = $pdo->query("SHOW TABLES LIKE \"users\"");
    $tablesExist = $stmt->rowCount() > 0;
    
} catch (PDOException $e) {
    $dbAvailable = false;
}

if ($isLaragon && $dbAvailable && $tablesExist) {
    $workMode = "online";
} elseif ($isLaragon && $dbAvailable && !$tablesExist) {
    $workMode = "setup";
} else {
    $workMode = "offline";
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea, #764ba2); margin: 0; padding: 2rem; min-height: 100vh; direction: rtl; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 1rem; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 2rem; text-align: center; }
        .content { padding: 2rem; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; text-decoration: none; border-radius: 0.5rem; margin: 0.5rem; }
        .alert { padding: 1rem; margin: 1rem 0; border-radius: 0.5rem; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status-badge { background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.85rem; margin: 0 0.25rem; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bread-slice"></i> <?php echo APP_NAME; ?></h1>
            <p>النظام المحاسبي الهجين المتكامل</p>
            <div>
                <span class="status-badge">v<?php echo APP_VERSION; ?></span>
                <span class="status-badge">
                    <?php if ($workMode === "online"): ?>
                        <i class="fas fa-database"></i> متصل
                    <?php elseif ($workMode === "setup"): ?>
                        <i class="fas fa-cog"></i> إعداد
                    <?php else: ?>
                        <i class="fas fa-laptop"></i> محلي
                    <?php endif; ?>
                </span>
            </div>
        </div>
        
        <div class="content">
            <?php if ($workMode === "setup"): ?>
                <div class="alert alert-info">
                    <h3><i class="fas fa-tools"></i> إعداد النظام لأول مرة</h3>
                    <p>تم اكتشاف Laragon بنجاح! يحتاج النظام لإنشاء قاعدة البيانات والجداول.</p>
                </div>
                <div style="text-align: center;">
                    <a href="setup-database.php" class="btn">
                        <i class="fas fa-rocket"></i> بدء الإعداد التلقائي
                    </a>
                    <a href="offline-app.html" class="btn">
                        <i class="fas fa-laptop"></i> العمل في الوضع المحلي
                    </a>
                </div>
                
            <?php elseif ($workMode === "online"): ?>
                <div class="alert alert-success">
                    <h3><i class="fas fa-check-circle"></i> النظام جاهز للعمل!</h3>
                    <p><strong>الخادم:</strong> Laragon</p>
                    <p><strong>قاعدة البيانات:</strong> MySQL متصلة</p>
                    <p><strong>الحالة:</strong> جاهز للاستخدام</p>
                </div>
                <div style="text-align: center;">
                    <a href="login.php" class="btn">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                    <a href="dashboard.php" class="btn">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </a>
                </div>
                
            <?php else: ?>
                <div class="alert alert-warning">
                    <h3><i class="fas fa-laptop"></i> الوضع المحلي</h3>
                    <p>النظام يعمل محلياً بدون خادم.</p>
                </div>
                <div style="text-align: center;">
                    <a href="offline-app.html" class="btn">
                        <i class="fas fa-play"></i> بدء النظام المحلي
                    </a>
                </div>
            <?php endif; ?>
            
            <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 0.5rem;">
                <h4>معلومات النظام:</h4>
                <p><strong>الخادم:</strong> <?php echo $_SERVER["SERVER_SOFTWARE"] ?? "غير محدد"; ?></p>
                <p><strong>PHP:</strong> <?php echo phpversion(); ?></p>
                <p><strong>الوقت:</strong> <?php echo date("Y-m-d H:i:s"); ?></p>
                <p><strong>المضيف:</strong> <?php echo $_SERVER["HTTP_HOST"] ?? "غير محدد"; ?></p>
            </div>
            
            <div style="text-align: center; margin-top: 1rem;">
                <a href="fix-system.php" class="btn" style="background: #28a745;">
                    <i class="fas fa-tools"></i> تشخيص النظام
                </a>
            </div>
        </div>
    </div>
</body>
</html>';

    if (file_put_contents($projectPath . 'laragon-app.php', $laragonAppContent)) {
        $fixes[] = '✅ تم إنشاء: laragon-app.php';
        $diagnostics['Files']['laragon-app.php'] = '✅ تم إنشاؤه';
    }
}

// تحديد الحالة العامة
$systemStatus = 'جاهز';
if (!empty($errors)) {
    $systemStatus = 'يحتاج إصلاح';
} elseif (!empty($fixes)) {
    $systemStatus = 'تم الإصلاح';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص وإصلاح النظام</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: bold;
            margin-top: 1rem;
        }

        .status-ready { background: #28a745; }
        .status-fixed { background: #17a2b8; }
        .status-error { background: #dc3545; }

        .content {
            padding: 2rem;
        }

        .section {
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }

        .section-body {
            padding: 1rem;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            border-right: 4px solid;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-right-color: #dc3545;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-right-color: #28a745;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-right-color: #17a2b8;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> تشخيص وإصلاح النظام</h1>
            <p>فحص شامل وإصلاح تلقائي لمشاكل النظام</p>
            <div class="status-badge status-<?php echo $systemStatus === 'جاهز' ? 'ready' : ($systemStatus === 'تم الإصلاح' ? 'fixed' : 'error'); ?>">
                <?php echo $systemStatus; ?>
            </div>
        </div>
        
        <div class="content">
            <!-- الأخطاء -->
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-circle"></i> أخطاء تحتاج إصلاح:</h4>
                <ul>
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- الإصلاحات -->
            <?php if (!empty($fixes)): ?>
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle"></i> تم تطبيق الإصلاحات التالية:</h4>
                <ul>
                    <?php foreach ($fixes as $fix): ?>
                    <li><?php echo htmlspecialchars($fix); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <!-- النجاح -->
            <?php if (empty($errors) && !empty($fixes)): ?>
            <div class="alert alert-info">
                <h4><i class="fas fa-rocket"></i> النظام جاهز الآن!</h4>
                <p>تم إصلاح جميع المشاكل. يمكنك الآن استخدام النظام.</p>
            </div>
            <?php endif; ?>

            <!-- تفاصيل التشخيص -->
            <div class="grid">
                <?php foreach ($diagnostics as $sectionName => $sectionData): ?>
                <div class="section">
                    <div class="section-header">
                        <i class="fas fa-<?php echo $sectionName === 'Server Status' ? 'server' : ($sectionName === 'Database' ? 'database' : ($sectionName === 'Files' ? 'file' : 'folder')); ?>"></i>
                        <?php echo $sectionName; ?>
                    </div>
                    <div class="section-body">
                        <?php foreach ($sectionData as $key => $value): ?>
                        <div class="check-item">
                            <span><?php echo htmlspecialchars($key); ?></span>
                            <span><?php echo $value; ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- أزرار العمل -->
            <div style="text-align: center; margin-top: 2rem;">
                <?php if (empty($errors)): ?>
                <a href="http://localhost/bakery-system/" class="btn btn-success" target="_blank">
                    <i class="fas fa-external-link-alt"></i> فتح النظام
                </a>
                <?php endif; ?>
                
                <a href="http://localhost/bakery-system/laragon-app.php" class="btn" target="_blank">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
                
                <button onclick="location.reload()" class="btn btn-warning">
                    <i class="fas fa-sync-alt"></i> إعادة الفحص
                </button>
            </div>

            <!-- معلومات إضافية -->
            <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 0.5rem;">
                <h4>خطوات استكشاف الأخطاء:</h4>
                <ol>
                    <li>تأكد من تشغيل Laragon (Apache + MySQL)</li>
                    <li>تحقق من وجود الملفات في المجلد الصحيح</li>
                    <li>جرب الروابط المختلفة</li>
                    <li>استخدم أداة التشخيص هذه</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        console.log('🔧 أداة التشخيص والإصلاح');
        console.log('📊 الحالة:', '<?php echo $systemStatus; ?>');
        
        // إذا تم الإصلاح، فتح النظام تلقائياً
        <?php if (empty($errors) && !empty($fixes)): ?>
        setTimeout(() => {
            window.open('http://localhost/bakery-system/', '_blank');
        }, 3000);
        <?php endif; ?>
    </script>
</body>
</html>

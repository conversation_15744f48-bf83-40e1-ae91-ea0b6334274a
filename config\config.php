<?php
/**
 * ملف الإعدادات الرئيسي للنظام الهجين
 * Main Configuration File for Hybrid System
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakery_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SYSTEM_TIMEZONE', 'Asia/Riyadh');
define('SYSTEM_LANGUAGE', 'ar');
define('SYSTEM_CURRENCY', 'YER');
define('SYSTEM_CURRENCY_SYMBOL', 'ر.ي');
define('SYSTEM_DECIMAL_PLACES', 3);

// إعدادات الأمان
define('SESSION_LIFETIME', 3600); // ساعة واحدة
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// مسارات النظام
define('BASE_PATH', dirname(__DIR__));
define('ASSETS_PATH', BASE_PATH . '/assets');
define('UPLOADS_PATH', BASE_PATH . '/uploads');
define('LOGS_PATH', BASE_PATH . '/logs');

// إعدادات التطوير
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
define('SHOW_ERRORS', DEBUG_MODE);

// تعيين المنطقة الزمنية
date_default_timezone_set(SYSTEM_TIMEZONE);

// إعدادات PHP
ini_set('display_errors', SHOW_ERRORS ? 1 : 0);
ini_set('log_errors', LOG_ERRORS ? 1 : 0);

// إعدادات الجلسة
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
ini_set('session.cookie_lifetime', SESSION_LIFETIME);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);

// وظائف مساعدة
if (!function_exists('formatCurrency')) {
    function formatCurrency($amount, $showSymbol = true) {
        $formatted = number_format($amount, SYSTEM_DECIMAL_PLACES, '.', ',');
        return $showSymbol ? $formatted . ' ' . SYSTEM_CURRENCY_SYMBOL : $formatted;
    }
}

if (!function_exists('formatDate')) {
    function formatDate($date, $format = 'Y-m-d') {
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        return $date->format($format);
    }
}

if (!function_exists('sanitizeInput')) {
    function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map('sanitizeInput', $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('checkPermission')) {
    function checkPermission($permission) {
        if (!isset($_SESSION['permissions'])) {
            return false;
        }
        
        $permissions = $_SESSION['permissions'];
        
        // المدير له جميع الصلاحيات
        if (in_array('all', $permissions)) {
            return true;
        }
        
        return in_array($permission, $permissions);
    }
}

if (!function_exists('createDirectories')) {
    function createDirectories() {
        $directories = [
            UPLOADS_PATH,
            LOGS_PATH,
            ASSETS_PATH . '/css',
            ASSETS_PATH . '/js',
            ASSETS_PATH . '/images'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
}

// إنشاء المجلدات المطلوبة
createDirectories();

// تنظيف البيانات المرسلة
if ($_POST) {
    $_POST = sanitizeInput($_POST);
}

if ($_GET) {
    $_GET = sanitizeInput($_GET);
}

// إعدادات إضافية للأمان
if (!DEBUG_MODE) {
    header('X-Powered-By: Anwar Bakery System');
    header('X-Frame-Options: SAMEORIGIN');
    header('X-Content-Type-Options: nosniff');
    header('X-XSS-Protection: 1; mode=block');
}
?>

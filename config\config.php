<?php
/**
 * ملف الإعدادات العامة للنظام
 * General System Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Aden');

// تعيين الترميز
mb_internal_encoding('UTF-8');

// إعدادات التطبيق
define('APP_NAME', 'نظام إدارة المخبز');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/anwarsoft');
define('APP_PATH', dirname(__DIR__));

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_TIMEOUT', 900); // 15 دقيقة

// إعدادات الملفات
define('UPLOAD_PATH', APP_PATH . '/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']);

// إعدادات العملة
define('CURRENCY_CODE', 'YER');
define('CURRENCY_NAME', 'ريال يمني');
define('CURRENCY_SYMBOL', 'ر.ي');
define('DECIMAL_PLACES', 2);

// إعدادات التاريخ والوقت
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات الطباعة
define('PRINT_FORMAT', 'thermal'); // thermal أو normal
define('PAPER_SIZE', 'A4');
define('PRINT_LOGO', true);

// بادئات الأرقام
define('INVOICE_PREFIX', 'INV');
define('VOUCHER_PREFIX', 'VOU');
define('JOURNAL_PREFIX', 'JE');
define('CUSTOMER_PREFIX', 'CUS');
define('SUPPLIER_PREFIX', 'SUP');
define('EMPLOYEE_PREFIX', 'EMP');
define('ITEM_PREFIX', 'ITM');
define('ASSET_PREFIX', 'AST');

// إعدادات الصفحات
define('RECORDS_PER_PAGE', 25);
define('MAX_RECORDS_PER_PAGE', 100);

// مستويات الصلاحيات
define('PERMISSION_LEVELS', [
    'view' => 'عرض',
    'add' => 'إضافة',
    'edit' => 'تعديل',
    'delete' => 'حذف',
    'print' => 'طباعة',
    'export' => 'تصدير'
]);

// أدوار المستخدمين
define('USER_ROLES', [
    'admin' => 'مدير النظام',
    'manager' => 'مدير',
    'accountant' => 'محاسب',
    'cashier' => 'أمين صندوق',
    'user' => 'مستخدم'
]);

// وحدات النظام
define('SYSTEM_MODULES', [
    'dashboard' => 'لوحة التحكم',
    'company' => 'بيانات المنشأة',
    'accounts' => 'شجرة الحسابات',
    'users' => 'إدارة المستخدمين',
    'cash_bank' => 'الصناديق والبنوك',
    'employees' => 'إدارة الموظفين',
    'inventory' => 'إدارة المخزون',
    'invoices' => 'الفواتير',
    'vouchers' => 'السندات',
    'assets' => 'الأصول الثابتة',
    'reports' => 'التقارير',
    'settings' => 'الإعدادات'
]);

// رسائل النظام
define('MESSAGES', [
    'success' => [
        'save' => 'تم الحفظ بنجاح',
        'update' => 'تم التحديث بنجاح',
        'delete' => 'تم الحذف بنجاح',
        'login' => 'تم تسجيل الدخول بنجاح',
        'logout' => 'تم تسجيل الخروج بنجاح'
    ],
    'error' => [
        'save' => 'خطأ في الحفظ',
        'update' => 'خطأ في التحديث',
        'delete' => 'خطأ في الحذف',
        'login' => 'خطأ في تسجيل الدخول',
        'permission' => 'ليس لديك صلاحية للوصول',
        'not_found' => 'البيانات غير موجودة',
        'validation' => 'خطأ في التحقق من البيانات'
    ],
    'warning' => [
        'confirm_delete' => 'هل أنت متأكد من الحذف؟',
        'unsaved_changes' => 'يوجد تغييرات غير محفوظة'
    ]
]);

/**
 * دالة للحصول على إعداد من قاعدة البيانات
 */
function getSetting($key, $default = null) {
    try {
        $db = getDB();
        $result = $db->selectOne(
            "SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?",
            [$key]
        );
        
        if ($result) {
            $value = $result['setting_value'];
            switch ($result['setting_type']) {
                case 'boolean':
                    return filter_var($value, FILTER_VALIDATE_BOOLEAN);
                case 'number':
                    return is_numeric($value) ? (float)$value : $default;
                case 'json':
                    return json_decode($value, true) ?: $default;
                default:
                    return $value;
            }
        }
        
        return $default;
    } catch (Exception $e) {
        return $default;
    }
}

/**
 * دالة لحفظ إعداد في قاعدة البيانات
 */
function setSetting($key, $value, $type = 'string') {
    try {
        $db = getDB();
        
        // تحويل القيمة حسب النوع
        switch ($type) {
            case 'boolean':
                $value = $value ? 'true' : 'false';
                break;
            case 'json':
                $value = json_encode($value);
                break;
            default:
                $value = (string)$value;
        }
        
        // التحقق من وجود الإعداد
        $exists = $db->exists('system_settings', 'setting_key = ?', [$key]);
        
        if ($exists) {
            return $db->update(
                "UPDATE system_settings SET setting_value = ?, setting_type = ?, updated_at = NOW() WHERE setting_key = ?",
                [$value, $type, $key]
            );
        } else {
            return $db->insert(
                "INSERT INTO system_settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)",
                [$key, $value, $type]
            );
        }
    } catch (Exception $e) {
        return false;
    }
}

/**
 * دالة لتنسيق المبلغ
 */
function formatMoney($amount, $showSymbol = true) {
    $formatted = number_format($amount, DECIMAL_PLACES, '.', ',');
    return $showSymbol ? $formatted . ' ' . CURRENCY_SYMBOL : $formatted;
}

/**
 * دالة لتنسيق التاريخ
 */
function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    if (empty($date) || $date === '0000-00-00') {
        return '';
    }
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date($format, $timestamp);
}

/**
 * دالة لتنسيق التاريخ والوقت
 */
function formatDateTime($datetime, $format = DISPLAY_DATETIME_FORMAT) {
    if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
        return '';
    }
    
    $timestamp = is_numeric($datetime) ? $datetime : strtotime($datetime);
    return date($format, $timestamp);
}

/**
 * دالة لتوليد رقم تلقائي
 */
function generateNumber($prefix, $table, $column, $length = 6) {
    try {
        $db = getDB();
        $result = $db->selectOne(
            "SELECT MAX(CAST(SUBSTRING({$column}, LENGTH(?) + 1) AS UNSIGNED)) as max_num FROM {$table} WHERE {$column} LIKE ?",
            [$prefix, $prefix . '%']
        );
        
        $nextNumber = ($result['max_num'] ?? 0) + 1;
        return $prefix . str_pad($nextNumber, $length, '0', STR_PAD_LEFT);
    } catch (Exception $e) {
        return $prefix . str_pad(1, $length, '0', STR_PAD_LEFT);
    }
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة لتنظيف النص
 */
function cleanInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * دالة للتحقق من نوع الملف
 */
function isAllowedFile($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, ALLOWED_EXTENSIONS);
}
?>

/**
 * ملف JavaScript الأساسي للنظام المحاسبي
 * Main JavaScript File for Bakery Accounting System
 */

// متغيرات عامة
let currentUser = null;
let systemSettings = {};

// تشغيل الكود عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
    setupEventListeners();
    loadUserData();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // تحديد اللغة
    document.documentElement.lang = 'ar';
    document.documentElement.dir = 'rtl';
    
    // تحميل الإعدادات
    loadSystemSettings();
    
    // تهيئة التوقيت
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // تهيئة التنبيهات
    initializeAlerts();
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // أزرار الحفظ
    document.querySelectorAll('.btn-save').forEach(btn => {
        btn.addEventListener('click', handleSave);
    });
    
    // أزرار الحذف
    document.querySelectorAll('.btn-delete').forEach(btn => {
        btn.addEventListener('click', handleDelete);
    });
    
    // أزرار الطباعة
    document.querySelectorAll('.btn-print').forEach(btn => {
        btn.addEventListener('click', handlePrint);
    });
    
    // النماذج
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', handleFormSubmit);
    });
    
    // حقول البحث
    document.querySelectorAll('.search-input').forEach(input => {
        input.addEventListener('input', handleSearch);
    });
    
    // القوائم المنسدلة
    document.querySelectorAll('.form-select').forEach(select => {
        select.addEventListener('change', handleSelectChange);
    });
    
    // الشريط الجانبي
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }
}

/**
 * تحميل بيانات المستخدم
 */
function loadUserData() {
    fetch('api/user.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentUser = data.user;
                updateUserInterface();
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل بيانات المستخدم:', error);
        });
}

/**
 * تحميل إعدادات النظام
 */
function loadSystemSettings() {
    fetch('api/settings.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                systemSettings = data.settings;
                applySystemSettings();
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل إعدادات النظام:', error);
        });
}

/**
 * تطبيق إعدادات النظام
 */
function applySystemSettings() {
    // تطبيق الثيم
    if (systemSettings.theme === 'dark') {
        document.body.classList.add('dark-theme');
    }
    
    // تطبيق اللغة
    if (systemSettings.language) {
        document.documentElement.lang = systemSettings.language;
    }
}

/**
 * تحديث واجهة المستخدم
 */
function updateUserInterface() {
    if (currentUser) {
        // عرض اسم المستخدم
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(el => {
            el.textContent = currentUser.full_name;
        });
        
        // إخفاء/إظهار العناصر حسب الصلاحيات
        updatePermissions();
    }
}

/**
 * تحديث الصلاحيات
 */
function updatePermissions() {
    if (!currentUser || !currentUser.permissions) return;
    
    const permissions = JSON.parse(currentUser.permissions);
    
    // إخفاء العناصر التي لا يملك المستخدم صلاحية لها
    document.querySelectorAll('[data-permission]').forEach(element => {
        const requiredPermission = element.dataset.permission;
        const [module, action] = requiredPermission.split('.');
        
        if (!hasPermission(module, action, permissions)) {
            element.style.display = 'none';
        }
    });
}

/**
 * التحقق من الصلاحية
 */
function hasPermission(module, action, permissions) {
    if (currentUser.role === 'admin') return true;
    if (permissions.all) return true;
    
    return permissions[module] && permissions[module][action];
}

/**
 * تحديث التاريخ والوقت
 */
function updateDateTime() {
    const now = new Date();
    const dateTimeElements = document.querySelectorAll('.current-datetime');
    
    dateTimeElements.forEach(element => {
        element.textContent = formatDateTime(now);
    });
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime(date) {
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    };
    
    return date.toLocaleDateString('ar-SA', options);
}

/**
 * تنسيق المبلغ
 */
function formatMoney(amount, showSymbol = true) {
    const formatted = new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
    
    return showSymbol ? formatted + ' ر.ي' : formatted;
}

/**
 * معالج الحفظ
 */
function handleSave(event) {
    const button = event.target;
    const form = button.closest('form');
    
    if (form) {
        if (validateForm(form)) {
            submitForm(form);
        }
    }
}

/**
 * معالج الحذف
 */
function handleDelete(event) {
    event.preventDefault();
    
    if (confirm('هل أنت متأكد من الحذف؟')) {
        const button = event.target;
        const url = button.href || button.dataset.url;
        
        if (url) {
            deleteRecord(url);
        }
    }
}

/**
 * معالج الطباعة
 */
function handlePrint(event) {
    const button = event.target;
    const printUrl = button.dataset.printUrl;
    
    if (printUrl) {
        printDocument(printUrl);
    } else {
        window.print();
    }
}

/**
 * معالج إرسال النموذج
 */
function handleFormSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    if (validateForm(form)) {
        submitForm(form);
    }
}

/**
 * معالج البحث
 */
function handleSearch(event) {
    const input = event.target;
    const searchTerm = input.value.trim();
    const targetTable = input.dataset.target;
    
    if (targetTable) {
        filterTable(targetTable, searchTerm);
    }
}

/**
 * معالج تغيير القائمة المنسدلة
 */
function handleSelectChange(event) {
    const select = event.target;
    const dependentSelect = select.dataset.dependent;
    
    if (dependentSelect) {
        loadDependentOptions(select.value, dependentSelect);
    }
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
    let isValid = true;
    const errors = [];
    
    // التحقق من الحقول المطلوبة
    form.querySelectorAll('[required]').forEach(field => {
        if (!field.value.trim()) {
            isValid = false;
            errors.push(`حقل ${field.dataset.label || field.name} مطلوب`);
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    // التحقق من البريد الإلكتروني
    form.querySelectorAll('input[type="email"]').forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            isValid = false;
            errors.push('البريد الإلكتروني غير صحيح');
            field.classList.add('is-invalid');
        }
    });
    
    // التحقق من الأرقام
    form.querySelectorAll('input[type="number"]').forEach(field => {
        if (field.value && isNaN(field.value)) {
            isValid = false;
            errors.push(`حقل ${field.dataset.label || field.name} يجب أن يكون رقم`);
            field.classList.add('is-invalid');
        }
    });
    
    if (!isValid) {
        showAlert('خطأ في البيانات', errors.join('<br>'), 'danger');
    }
    
    return isValid;
}

/**
 * إرسال النموذج
 */
function submitForm(form) {
    const formData = new FormData(form);
    const url = form.action || window.location.href;
    
    showLoading(true);
    
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        
        if (data.success) {
            showAlert('نجح', data.message, 'success');
            
            // إعادة تحميل الصفحة أو إعادة التوجيه
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1500);
            } else {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        } else {
            showAlert('خطأ', data.message, 'danger');
        }
    })
    .catch(error => {
        showLoading(false);
        showAlert('خطأ', 'حدث خطأ في الاتصال', 'danger');
        console.error('خطأ:', error);
    });
}

/**
 * حذف سجل
 */
function deleteRecord(url) {
    showLoading(true);
    
    fetch(url, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);
        
        if (data.success) {
            showAlert('نجح', data.message, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('خطأ', data.message, 'danger');
        }
    })
    .catch(error => {
        showLoading(false);
        showAlert('خطأ', 'حدث خطأ في الاتصال', 'danger');
        console.error('خطأ:', error);
    });
}

/**
 * طباعة مستند
 */
function printDocument(url) {
    const printWindow = window.open(url, '_blank');
    printWindow.onload = function() {
        printWindow.print();
    };
}

/**
 * تصفية الجدول
 */
function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * تحميل خيارات تابعة
 */
function loadDependentOptions(parentValue, targetSelectId) {
    const targetSelect = document.getElementById(targetSelectId);
    if (!targetSelect) return;
    
    // مسح الخيارات الحالية
    targetSelect.innerHTML = '<option value="">اختر...</option>';
    
    if (!parentValue) return;
    
    fetch(`api/dependent-options.php?parent=${parentValue}&target=${targetSelectId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.text;
                    targetSelect.appendChild(optionElement);
                });
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الخيارات:', error);
        });
}

/**
 * إظهار/إخفاء الشريط الجانبي
 */
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

/**
 * إظهار التنبيه
 */
function showAlert(title, message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        <strong>${title}:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertElement);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        alertElement.remove();
    }, 5000);
}

/**
 * إنشاء حاوي التنبيهات
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.style.position = 'fixed';
    container.style.top = '20px';
    container.style.right = '20px';
    container.style.zIndex = '9999';
    container.style.maxWidth = '400px';
    
    document.body.appendChild(container);
    return container;
}

/**
 * إظهار/إخفاء شاشة التحميل
 */
function showLoading(show) {
    let loader = document.getElementById('loading-overlay');
    
    if (show) {
        if (!loader) {
            loader = document.createElement('div');
            loader.id = 'loading-overlay';
            loader.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>جاري التحميل...</p>
                </div>
            `;
            loader.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            document.body.appendChild(loader);
        }
        loader.style.display = 'flex';
    } else {
        if (loader) {
            loader.style.display = 'none';
        }
    }
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إنشاء حاوي التنبيهات
    createAlertContainer();
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * دوال مساعدة للتصدير
 */
window.BakerySystem = {
    formatMoney,
    formatDateTime,
    showAlert,
    showLoading,
    hasPermission,
    validateForm,
    submitForm
};

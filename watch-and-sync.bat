@echo off
chcp 65001 >nul
title مراقب الملفات والمزامنة التلقائية
color 0E

echo.
echo ========================================
echo     مراقب الملفات والمزامنة التلقائية
echo        نظام إدارة المخبز المحاسبي
echo ========================================
echo.

REM تحديد المجلدات
set SOURCE_DIR=%~dp0
set TARGET_DIR=C:\laragon\www\bakery-system\

echo 📁 مراقبة: %SOURCE_DIR%
echo 📁 مزامنة مع: %TARGET_DIR%
echo.

REM التحقق من وجود Laragon
if not exist "C:\laragon\www\" (
    echo ❌ خطأ: Laragon غير موجود في المسار الافتراضي
    echo    C:\laragon\www\
    echo.
    echo 💡 حلول مقترحة:
    echo    1. تثبيت Laragon في المسار الافتراضي
    echo    2. تشغيل Laragon إذا كان مثبت
    echo    3. تعديل المسار في هذا الملف
    echo.
    pause
    exit /b 1
)

REM إنشاء مجلد الهدف إذا لم يكن موجوداً
if not exist "%TARGET_DIR%" (
    echo 📁 إنشاء مجلد المشروع...
    mkdir "%TARGET_DIR%"
    if %ERRORLEVEL%==0 (
        echo ✅ تم إنشاء: %TARGET_DIR%
    ) else (
        echo ❌ فشل في إنشاء المجلد
        pause
        exit /b 1
    )
) else (
    echo ✅ مجلد المشروع موجود: %TARGET_DIR%
)

echo.
echo 🔄 بدء المراقبة والمزامنة التلقائية...
echo.
echo 📋 الملفات المراقبة:
echo    • ملفات PHP (*.php)
echo    • ملفات HTML (*.html)
echo    • ملفات CSS (*.css)
echo    • ملفات JavaScript (*.js)
echo    • ملفات JSON (*.json)
echo    • ملفات SQL (*.sql)
echo.
echo 💡 نصائح:
echo    • اتركني أعمل في الخلفية
echo    • أي تغيير سيتم نسخه فوراً
echo    • اضغط Ctrl+C للتوقف
echo.
echo ========================================
echo           سجل المزامنة
echo ========================================

REM إنشاء مزامنة أولية
echo.
echo [%TIME%] 🚀 بدء المزامنة الأولية...

REM نسخ جميع الملفات الموجودة
for %%f in ("%SOURCE_DIR%*.php") do (
    call :SYNC_FILE "%%f" "%TARGET_DIR%%%~nxf" "%%~nxf"
)

for %%f in ("%SOURCE_DIR%*.html") do (
    call :SYNC_FILE "%%f" "%TARGET_DIR%%%~nxf" "%%~nxf"
)

for %%f in ("%SOURCE_DIR%*.css") do (
    call :SYNC_FILE "%%f" "%TARGET_DIR%%%~nxf" "%%~nxf"
)

for %%f in ("%SOURCE_DIR%*.js") do (
    call :SYNC_FILE "%%f" "%TARGET_DIR%%%~nxf" "%%~nxf"
)

for %%f in ("%SOURCE_DIR%*.json" "%SOURCE_DIR%*.sql" "%SOURCE_DIR%*.txt") do (
    if exist "%%f" (
        call :SYNC_FILE "%%f" "%TARGET_DIR%%%~nxf" "%%~nxf"
    )
)

echo [%TIME%] ✅ اكتملت المزامنة الأولية
echo.
echo [%TIME%] 👁️  بدء مراقبة التغييرات...

REM حفظ أوقات التعديل الحالية
set "TEMP_FILE=%TEMP%\bakery_file_times.txt"
call :SAVE_FILE_TIMES

:WATCH_LOOP

REM انتظار قصير
timeout /t 2 >nul

REM فحص التغييرات
call :CHECK_CHANGES

goto WATCH_LOOP

:CHECK_CHANGES

REM فحص ملفات PHP
for %%f in ("%SOURCE_DIR%*.php") do (
    call :CHECK_FILE_CHANGE "%%f" "%%~nxf"
)

REM فحص ملفات HTML
for %%f in ("%SOURCE_DIR%*.html") do (
    call :CHECK_FILE_CHANGE "%%f" "%%~nxf"
)

REM فحص ملفات CSS
for %%f in ("%SOURCE_DIR%*.css") do (
    call :CHECK_FILE_CHANGE "%%f" "%%~nxf"
)

REM فحص ملفات JS
for %%f in ("%SOURCE_DIR%*.js") do (
    call :CHECK_FILE_CHANGE "%%f" "%%~nxf"
)

REM فحص ملفات أخرى
for %%f in ("%SOURCE_DIR%*.json" "%SOURCE_DIR%*.sql" "%SOURCE_DIR%*.txt") do (
    if exist "%%f" (
        call :CHECK_FILE_CHANGE "%%f" "%%~nxf"
    )
)

goto :eof

:CHECK_FILE_CHANGE
set "FILE_PATH=%~1"
set "FILE_NAME=%~2"

if not exist "%FILE_PATH%" goto :eof

REM الحصول على وقت التعديل الحالي
for %%i in ("%FILE_PATH%") do set CURRENT_TIME=%%~ti

REM قراءة الوقت المحفوظ
set "SAVED_TIME="
for /f "tokens=1,2 delims==" %%a in ('type "%TEMP_FILE%" 2^>nul ^| findstr /c:"%FILE_NAME%"') do (
    set "SAVED_TIME=%%b"
)

REM مقارنة الأوقات
if not "%CURRENT_TIME%"=="%SAVED_TIME%" (
    call :SYNC_FILE "%FILE_PATH%" "%TARGET_DIR%%FILE_NAME%" "%FILE_NAME%"
    call :UPDATE_FILE_TIME "%FILE_NAME%" "%CURRENT_TIME%"
)

goto :eof

:SYNC_FILE
set "SOURCE=%~1"
set "TARGET=%~2"
set "NAME=%~3"

copy "%SOURCE%" "%TARGET%" >nul 2>&1
if %ERRORLEVEL%==0 (
    echo [%TIME%] ✅ تم تحديث: %NAME%
    
    REM إشعار بصري (اختياري)
    if exist "%TARGET%" (
        echo [%TIME%] 📁 المسار: %TARGET%
    )
) else (
    echo [%TIME%] ❌ فشل تحديث: %NAME%
)

goto :eof

:SAVE_FILE_TIMES
echo. > "%TEMP_FILE%"

for %%f in ("%SOURCE_DIR%*.php" "%SOURCE_DIR%*.html" "%SOURCE_DIR%*.css" "%SOURCE_DIR%*.js" "%SOURCE_DIR%*.json" "%SOURCE_DIR%*.sql" "%SOURCE_DIR%*.txt") do (
    if exist "%%f" (
        for %%i in ("%%f") do (
            echo %%~nxi=%%~ti >> "%TEMP_FILE%"
        )
    )
)

goto :eof

:UPDATE_FILE_TIME
set "FILE_NAME=%~1"
set "NEW_TIME=%~2"

REM إنشاء ملف مؤقت جديد
set "NEW_TEMP=%TEMP%\bakery_file_times_new.txt"
echo. > "%NEW_TEMP%"

REM نسخ جميع الأسطر عدا الملف المحدث
for /f "tokens=1,2 delims==" %%a in ('type "%TEMP_FILE%" 2^>nul') do (
    if not "%%a"=="%FILE_NAME%" (
        echo %%a=%%b >> "%NEW_TEMP%"
    )
)

REM إضافة الوقت الجديد
echo %FILE_NAME%=%NEW_TIME% >> "%NEW_TEMP%"

REM استبدال الملف القديم
move "%NEW_TEMP%" "%TEMP_FILE%" >nul

goto :eof

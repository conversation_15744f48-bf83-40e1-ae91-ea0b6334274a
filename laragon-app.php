<?php
/**
 * النظام المحاسبي الهجين للمخابز - محسن لـ Laragon
 * Hybrid Bakery Accounting System - Optimized for Laragon
 * 
 * @version 2.0.0
 * <AUTHOR> سوفت
 */

// بدء الجلسة
session_start();

// إعدادات النظام
define('APP_NAME', 'نظام إدارة المخبز المحاسبي');
define('APP_VERSION', '2.0.0');
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakery_system');
define('DB_USER', 'root');
define('DB_PASS', '');

// كشف البيئة
$isLaragon = isset($_SERVER['HTTP_HOST']) && (strpos($_SERVER['HTTP_HOST'], '.test') !== false || $_SERVER['HTTP_HOST'] === 'localhost');
$dbAvailable = false;
$tablesExist = false;

// فحص قاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbAvailable = true;
    
    // فحص وجود الجداول
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tablesExist = $stmt->rowCount() > 0;
    
} catch (PDOException $e) {
    $dbAvailable = false;
}

// تحديد وضع العمل
if ($isLaragon && $dbAvailable && $tablesExist) {
    $workMode = 'online';
} elseif ($isLaragon && $dbAvailable && !$tablesExist) {
    $workMode = 'setup';
} else {
    $workMode = 'offline';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - النسخة الهجينة</title>
    
    <!-- CSS Framework -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    
    <style>
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --success: #28a745;
            --danger: #dc3545;
            --warning: #ffc107;
            --info: #17a2b8;
            --light: #f8f9fa;
            --dark: #343a40;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--light) 0%, #e3f2fd 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .status-bar {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 0.75rem 1rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.85rem;
        }

        .main-container {
            margin-top: 80px;
            padding: 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .welcome-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .welcome-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .welcome-header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .welcome-body {
            padding: 2rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            margin: 0.25rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }

        .btn-success { background: var(--success); color: white; }
        .btn-warning { background: var(--warning); color: var(--dark); }
        .btn-info { background: var(--info); color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin: 2rem 0;
        }

        .system-info {
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 2rem 0;
            border-right: 4px solid var(--primary);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-online { color: var(--success); }
        .status-offline { color: var(--warning); }
        .status-setup { color: var(--info); }

        @media (max-width: 768px) {
            .main-container { padding: 1rem; }
            .welcome-header h1 { font-size: 1.5rem; }
            .features-grid { grid-template-columns: 1fr; }
            .action-buttons { flex-direction: column; align-items: center; }
            .btn { width: 100%; max-width: 300px; justify-content: center; }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .welcome-card { animation: fadeInUp 0.6s ease; }
        .feature-card { animation: fadeInUp 0.6s ease; animation-delay: calc(var(--delay, 0) * 0.1s); }
    </style>
</head>
<body>
    <!-- شريط الحالة -->
    <div class="status-bar">
        <div>
            <i class="fas fa-bread-slice"></i>
            <strong><?php echo APP_NAME; ?></strong>
            <span class="status-badge">v<?php echo APP_VERSION; ?></span>
        </div>
        <div>
            <?php if ($workMode === 'online'): ?>
                <span class="status-badge status-online">
                    <i class="fas fa-database"></i> متصل - Laragon
                </span>
            <?php elseif ($workMode === 'setup'): ?>
                <span class="status-badge status-setup">
                    <i class="fas fa-cog"></i> يحتاج إعداد
                </span>
            <?php else: ?>
                <span class="status-badge status-offline">
                    <i class="fas fa-laptop"></i> وضع محلي
                </span>
            <?php endif; ?>
            
            <span class="status-badge">
                <i class="fas fa-clock"></i>
                <span id="currentTime"><?php echo date('H:i:s'); ?></span>
            </span>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-container">
        <!-- بطاقة الترحيب -->
        <div class="welcome-card">
            <div class="welcome-header">
                <h1>
                    <i class="fas fa-bread-slice"></i>
                    مرحباً بك في نظام إدارة المخبز
                </h1>
                <p>النظام المحاسبي الهجين المتكامل - محسن لـ Laragon</p>
            </div>
            
            <div class="welcome-body">
                <?php if ($workMode === 'setup'): ?>
                    <!-- وضع الإعداد -->
                    <div class="system-info">
                        <h3><i class="fas fa-tools"></i> إعداد النظام لأول مرة</h3>
                        <p>تم اكتشاف Laragon بنجاح! يحتاج النظام لإنشاء قاعدة البيانات والجداول.</p>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="setup-database.php" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            بدء الإعداد التلقائي
                        </a>
                        <button onclick="startOfflineMode()" class="btn btn-warning">
                            <i class="fas fa-laptop"></i>
                            العمل في الوضع المحلي
                        </button>
                    </div>
                    
                <?php elseif ($workMode === 'online'): ?>
                    <!-- الوضع الأونلاين -->
                    <div class="system-info">
                        <h3><i class="fas fa-check-circle status-online"></i> النظام جاهز للعمل</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <strong>الخادم:</strong> Laragon (<?php echo $_SERVER['HTTP_HOST']; ?>)
                            </div>
                            <div class="info-item">
                                <strong>قاعدة البيانات:</strong> MySQL متصلة
                            </div>
                            <div class="info-item">
                                <strong>وضع العمل:</strong> هجين (أونلاين + أوفلاين)
                            </div>
                            <div class="info-item">
                                <strong>الأمان:</strong> SSL جاهز
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fas fa-tachometer-alt"></i>
                            دخول لوحة التحكم
                        </a>
                        <a href="login.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt"></i>
                            تسجيل الدخول
                        </a>
                    </div>
                    
                <?php else: ?>
                    <!-- الوضع الأوفلاين -->
                    <div class="system-info">
                        <h3><i class="fas fa-laptop"></i> الوضع المحلي</h3>
                        <p>النظام يعمل محلياً باستخدام تقنيات المتصفح المتقدمة.</p>
                    </div>
                    
                    <div class="action-buttons">
                        <button onclick="startOfflineMode()" class="btn btn-primary">
                            <i class="fas fa-play"></i>
                            بدء النظام المحلي
                        </button>
                        <a href="?refresh=1" class="btn btn-info">
                            <i class="fas fa-sync"></i>
                            فحص Laragon
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- بطاقات المميزات -->
        <div class="features-grid">
            <div class="feature-card" style="--delay: 1">
                <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                <h3>محاسبة متكاملة</h3>
                <p>نظام محاسبي شامل مع شجرة الحسابات والقيود التلقائية</p>
            </div>
            
            <div class="feature-card" style="--delay: 2">
                <div class="feature-icon"><i class="fas fa-boxes"></i></div>
                <h3>إدارة المخزون</h3>
                <p>تتبع المخزون والمواد الخام مع تنبيهات إعادة الطلب</p>
            </div>
            
            <div class="feature-card" style="--delay: 3">
                <div class="feature-icon"><i class="fas fa-file-invoice"></i></div>
                <h3>الفواتير والمبيعات</h3>
                <p>إنشاء فواتير احترافية مع ربط تلقائي بالمحاسبة</p>
            </div>
            
            <div class="feature-card" style="--delay: 4">
                <div class="feature-icon"><i class="fas fa-users"></i></div>
                <h3>إدارة الموظفين</h3>
                <p>نظام شامل للموظفين والرواتب والحضور والانصراف</p>
            </div>
        </div>
    </div>

    <script>
        // إعدادات النظام
        const SYSTEM_CONFIG = {
            workMode: '<?php echo $workMode; ?>',
            laragonMode: <?php echo $isLaragon ? 'true' : 'false'; ?>,
            dbAvailable: <?php echo $dbAvailable ? 'true' : 'false'; ?>,
            version: '<?php echo APP_VERSION; ?>'
        };

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.getElementById('currentTime').textContent = timeString;
        }

        // بدء النظام المحلي
        function startOfflineMode() {
            window.location.href = 'offline-app.html';
        }

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            setInterval(updateTime, 1000);
            
            console.log('🍞 نظام إدارة المخبز المحاسبي');
            console.log('📊 الإصدار:', SYSTEM_CONFIG.version);
            console.log('🔧 وضع العمل:', SYSTEM_CONFIG.workMode);
            console.log('🖥️ Laragon:', SYSTEM_CONFIG.laragonMode ? 'نشط' : 'غير نشط');
        });
    </script>
</body>
</html>
